#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的Web介面測試腳本
"""

import json
import os
from web_interface import NVRVWebInterface

def create_test_settings():
    """創建測試用的設定檔"""
    test_settings = [
        {
            "channel_name": "測試頻道1",
            "rtsp_url": "rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4",
            "save_path": "./recordings",
            "record_duration": 20,
            "record_fps": 8,
            "keep_days": 7,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "channel_name": "測試頻道2", 
            "rtsp_url": "rtsp://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
            "save_path": "./recordings",
            "record_duration": 20,
            "record_fps": 8,
            "keep_days": 7,
            "use_source_fps": False,
            "force_640x360": True
        }
    ]
    
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(test_settings, f, ensure_ascii=False, indent=2)
    
    print("✅ 測試設定檔已創建")

def main():
    print("🚀 啟動簡化版Web介面測試...")
    print("=" * 50)
    
    # 創建測試設定
    if not os.path.exists('settings_free.json'):
        create_test_settings()
    
    # 啟動Web介面
    try:
        web_interface = NVRVWebInterface()
        print("🌐 Web介面啟動資訊:")
        print("   網址: http://localhost:8080")
        print("   功能: 即時串流監控 (無錄影功能)")
        print("=" * 50)
        print("🎉 Web介面已啟動！")
        print("📱 請在瀏覽器中開啟 http://localhost:8080")
        print("⏹️  按 Ctrl+C 停止服務")
        print()
        
        web_interface.run(host='0.0.0.0', port=8080, debug=True)
        
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號，正在關閉...")
    except Exception as e:
        print(f"❌ Web介面啟動失敗: {e}")
    finally:
        print("👋 Web介面已停止")

if __name__ == '__main__':
    main()