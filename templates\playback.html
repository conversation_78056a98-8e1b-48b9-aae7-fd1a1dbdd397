{% extends "base.html" %}

{% block title %}歷史回放 - NVRV-Free{% endblock %}

{% block page_title %}歷史回放{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="refreshRecordings()">
        <i class="fas fa-sync-alt me-1"></i>
        重新整理
    </button>
    <button type="button" class="btn btn-outline-success" onclick="exportRecordings()">
        <i class="fas fa-download me-1"></i>
        匯出選取
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 搜尋篩選 -->
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    搜尋篩選
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="channelSelect" class="form-label">選擇頻道</label>
                    <select class="form-select" id="channelSelect" onchange="loadRecordings()">
                        <option value="">載入中...</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="dateSelect" class="form-label">選擇日期</label>
                    <input type="date" class="form-control" id="dateSelect" onchange="loadRecordings()">
                </div>
                
                <div class="mb-3">
                    <label for="timeRange" class="form-label">時間範圍</label>
                    <div class="row">
                        <div class="col-6">
                            <input type="time" class="form-control" id="startTime" onchange="filterRecordings()">
                        </div>
                        <div class="col-6">
                            <input type="time" class="form-control" id="endTime" onchange="filterRecordings()">
                        </div>
                    </div>
                </div>
                
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="loadRecordings()">
                        <i class="fas fa-search me-1"></i>
                        搜尋
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 統計資訊 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    統計資訊
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">錄影檔案數量</small>
                    <div class="fw-bold" id="recordingCount">0</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 錄影列表 -->
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    錄影檔案
                </h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAll">
                        全選
                    </label>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40"></th>
                                <th>檔案名稱</th>
                                <th>錄影時間</th>
                                <th>檔案大小</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="recordingTableBody">
                            <tr>
                                <td colspan="6" class="text-center text-muted p-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    載入中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 影片播放模態框 -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalTitle">影片播放</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <video id="videoPlayer" controls preload="auto" 
                       width="100%" height="480" style="background-color: #000;">
                    您的瀏覽器不支援影片播放，請升級瀏覽器。
                </video>
            </div>
            <div class="modal-footer">
                <div class="me-auto">
                    <button class="btn btn-outline-primary" onclick="downloadCurrentVideo()">
                        <i class="fas fa-download me-1"></i>
                        下載
                    </button>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

let channels = [];
let recordings = [];
let filteredRecordings = [];
let currentVideoPath = '';
let videoPlayer = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 設定預設日期為今天
    document.getElementById('dateSelect').value = new Date().toISOString().split('T')[0];
    
    loadChannels();
    
    // 檢查URL參數中是否指定了頻道
    const urlParams = new URLSearchParams(window.location.search);
    const channelParam = urlParams.get('channel');
    if (channelParam) {
        // 延遲設置選中的頻道，等待頻道列表載入完成
        setTimeout(() => {
            const channelSelect = document.getElementById('channelSelect');
            if (channelSelect) {
                channelSelect.value = channelParam;
                loadRecordings();
            }
        }, 1000);
    }
});

// 載入頻道列表
async function loadChannels() {
    console.log('Loading channels for playback...');
    try {
        const response = await fetch('/api/channels');
        const data = await response.json();
        
        console.log('Playback channels response:', data);
        
        if (data.success) {
            channels = data.channels;
            console.log(`Loaded ${channels.length} channels for playback`);
            renderChannelSelect();
        } else {
            console.error('Failed to load channels:', data.error);
            document.getElementById('channelSelect').innerHTML = '<option value="">載入失敗</option>';
        }
    } catch (error) {
        console.error('載入頻道失敗:', error);
        document.getElementById('channelSelect').innerHTML = '<option value="">載入錯誤</option>';
    }
}

// 渲染頻道選擇器
function renderChannelSelect() {
    const select = document.getElementById('channelSelect');
    
    select.innerHTML = '<option value="">所有頻道</option>' +
        channels.map(channel => 
            `<option value="${channel.id}">${channel.name}</option>`
        ).join('');
    
    // 預設選擇第一個頻道
    if (channels.length > 0) {
        select.value = channels[0].id;
        loadRecordings();
    }
}

// 載入錄影檔案
async function loadRecordings() {
    const channelId = document.getElementById('channelSelect').value;
    const date = document.getElementById('dateSelect').value.replace(/-/g, '');
    
    console.log(`Loading recordings for channel ${channelId}, date ${date}`);
    
    // 顯示載入中
    document.getElementById('recordingTableBody').innerHTML = `
        <tr>
            <td colspan="6" class="text-center text-muted p-4">
                <i class="fas fa-spinner fa-spin me-2"></i>
                載入中...
            </td>
        </tr>
    `;
    
    if (!channelId) {
        document.getElementById('recordingTableBody').innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted p-4">
                    請選擇頻道
                </td>
            </tr>
        `;
        return;
    }
    
    try {
        const response = await fetch(`/api/recordings?channel_id=${channelId}&date=${date}`);
        const data = await response.json();
        
        console.log('Recordings response:', data);
        
        if (data.success) {
            recordings = data.recordings;
            filteredRecordings = [...recordings];
            console.log(`Found ${recordings.length} recordings`);
            renderRecordingTable();
            updateStatistics();
        } else {
            console.error('Failed to load recordings:', data.error);
            document.getElementById('recordingTableBody').innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted p-4">
                        載入失敗: ${data.error}
                    </td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('載入錄影失敗:', error);
        document.getElementById('recordingTableBody').innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted p-4">
                    載入失敗: ${error.message}
                </td>
            </tr>
        `;
    }
}

// 渲染錄影表格
function renderRecordingTable() {
    const tbody = document.getElementById('recordingTableBody');
    
    if (filteredRecordings.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted p-4">
                    <i class="fas fa-video-slash me-2"></i>
                    沒有找到錄影檔案
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredRecordings.map((recording, index) => {
        const createdTime = new Date(recording.created);
        const timeStr = createdTime.toLocaleTimeString('zh-TW');
        
        return `
            <tr>
                <td>
                    <div class="form-check">
                        <input class="form-check-input recording-checkbox" type="checkbox" 
                               value="${recording.path}" id="check-${index}">
                    </div>
                </td>
                <td>
                    <div>
                        <strong>${recording.filename}</strong>
                        <br>
                        <small class="text-muted">${recording.path}</small>
                    </div>
                </td>
                <td>${timeStr}</td>
                <td>${formatFileSize(recording.size)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="playVideo('${recording.path}', '${recording.filename}')" title="播放">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadVideo('${recording.path}', '${recording.filename}')" title="下載">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 篩選錄影檔案
function filterRecordings() {
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    
    if (!startTime && !endTime) {
        filteredRecordings = [...recordings];
    } else {
        filteredRecordings = recordings.filter(recording => {
            const createdTime = new Date(recording.created);
            const timeStr = createdTime.toTimeString().substr(0, 5);
            
            if (startTime && timeStr < startTime) return false;
            if (endTime && timeStr > endTime) return false;
            
            return true;
        });
    }
    
    renderRecordingTable();
    updateStatistics();
}

// 更新統計資訊
function updateStatistics() {
    const count = filteredRecordings.length;
    document.getElementById('recordingCount').textContent = count;
}

// 播放影片
function playVideo(videoPath, filename) {
    console.log('Playing video:', filename, 'Path:', videoPath);
    
    currentVideoPath = videoPath;
    document.getElementById('videoModalTitle').textContent = filename;
    
    const videoElement = document.getElementById('videoPlayer');
    
    // 構建影片URL
    const videoUrl = `/api/video/${encodeURIComponent(filename)}`;
    console.log('Video URL:', videoUrl);
    
    // 直接設定影片來源
    videoElement.src = videoUrl;
    
    // 添加錯誤處理
    videoElement.onerror = function() {
        console.error('Video loading error');
        alert('影片載入失敗，請檢查檔案是否存在');
    };
    
    videoElement.onloadstart = function() {
        console.log('Video loading started');
    };
    
    videoElement.oncanplay = function() {
        console.log('Video can play');
    };
    
    // 顯示模態框
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    modal.show();
    
    // 模態框關閉時暫停播放
    modal._element.addEventListener('hidden.bs.modal', function() {
        videoElement.pause();
        videoElement.currentTime = 0;
    });
}

// 下載影片
function downloadVideo(videoPath, filename) {
    const link = document.createElement('a');
    link.href = `/api/video/${encodeURIComponent(filename)}`;
    link.download = filename;
    link.click();
}

// 下載當前播放的影片
function downloadCurrentVideo() {
    if (currentVideoPath) {
        const filename = currentVideoPath.split('/').pop();
        const link = document.createElement('a');
        link.href = `/api/video/${encodeURIComponent(filename)}`;
        link.download = filename;
        link.click();
    }
}



// 刪除影片
function deleteVideo(videoPath, filename) {
    if (confirm(`確定要刪除影片 "${filename}" 嗎？此操作無法復原。`)) {
        // TODO: 實現刪除功能
        alert('刪除功能開發中...');
    }
}

// 全選/取消全選
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.recording-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 匯出選取的錄影
function exportRecordings() {
    const selectedCheckboxes = document.querySelectorAll('.recording-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        alert('請選擇要匯出的錄影檔案');
        return;
    }
    
    const selectedPaths = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    // TODO: 實現批量匯出功能
    alert(`已選擇 ${selectedPaths.length} 個檔案進行匯出\n匯出功能開發中...`);
}

// 重新整理錄影列表
function refreshRecordings() {
    loadRecordings();
}
</script>
{% endblock %}