#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試 start_web_server.py 是否能正常運行
"""

import sys
import os

def test_direct_run():
    """直接測試腳本運行"""
    print("🧪 測試 start_web_server.py 直接運行")
    print("=" * 40)
    
    # 檢查必要文件
    required_files = ['start_web_server.py', 'nvrv_web_server.py', 'settings_free.json', 'templates']
    missing = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing:
        print(f"\n❌ 缺失文件: {missing}")
        return False
    
    print("\n🚀 嘗試直接運行...")
    print("如果成功，應該會開啟瀏覽器到 http://localhost:8080")
    print("按 Ctrl+C 停止")
    print("-" * 40)
    
    try:
        # 直接導入並運行
        sys.path.insert(0, '.')
        import start_web_server
        start_web_server.main()
    except KeyboardInterrupt:
        print("\n✅ 程式正常停止")
        return True
    except Exception as e:
        print(f"\n❌ 運行錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_direct_run()