#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVRV-Free Web介面 (舊版本，建議使用 nvrv_web_server.py)
提供NVR級別的Web管理和監控功能
"""

import warnings
warnings.warn("此文件已過時，請使用 nvrv_web_server.py", DeprecationWarning)

from flask import Flask, render_template, jsonify, request, send_file, Response
from flask_socketio import SocketIO, emit
import cv2
import os
import json
import threading
import time
from datetime import datetime, timedelta
import base64
import glob
import logging
from pathlib import Path
import mimetypes

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NVRVWebInterface:
    def __init__(self, nvrv_instance=None):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'nvrv-free-web-2024'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self.nvrv_instance = nvrv_instance
        self.live_streams = {}  # 儲存即時串流
        self.setup_routes()
        self.setup_socketio()
        
    def setup_routes(self):
        """設置Web路由"""
        
        @self.app.route('/')
        def index():
            """主頁面 - 即時監控"""
            return render_template('index.html')
        
        @self.app.route('/playback')
        def playback():
            """回放頁面"""
            return render_template('playback.html')
        
        @self.app.route('/settings')
        def settings():
            """設定頁面"""
            return render_template('settings.html')
        
        @self.app.route('/api/channels')
        def get_channels():
            """獲取所有頻道信息"""
            try:
                channels = []
                if self.nvrv_instance:
                    # 從NVRV主程式獲取即時狀態
                    for i, channel in enumerate(self.nvrv_instance.channels):
                        channels.append({
                            'id': i,
                            'name': channel.channel_name.get(),
                            'rtsp_url': channel.rtsp_url.get(),
                            'recording': channel.recording,
                            'status': 'online' if channel.cap and channel.cap.isOpened() else 'offline'
                        })
                else:
                    # 回退到從設定檔讀取
                    if os.path.exists('settings_free.json'):
                        with open('settings_free.json', 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            logger.info(f"Loaded {len(data)} channels from settings")
                            for i, channel_data in enumerate(data):
                                channel_info = {
                                    'id': i,
                                    'name': channel_data.get('channel_name', f'Channel {i+1}'),
                                    'rtsp_url': channel_data.get('rtsp_url', ''),
                                    'recording': False,
                                    'status': 'offline'
                                }
                                channels.append(channel_info)
                                logger.info(f"Channel {i}: {channel_info['name']} - {channel_info['rtsp_url']}")
                    else:
                        logger.warning("settings_free.json not found")
                
                logger.info(f"Returning {len(channels)} channels")
                return jsonify({'success': True, 'channels': channels})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/recordings')
        def get_recordings():
            """獲取錄影檔案列表"""
            try:
                channel_id = request.args.get('channel_id')
                date = request.args.get('date', datetime.now().strftime('%Y%m%d'))
                
                logger.info(f"Getting recordings for channel {channel_id}, date {date}")
                recordings = []
                
                if not channel_id:
                    return jsonify({'success': True, 'recordings': [], 'message': 'No channel selected'})
                
                # 讀取設定檔獲取儲存路徑
                if os.path.exists('settings_free.json'):
                    with open('settings_free.json', 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if int(channel_id) < len(data):
                            save_path = data[int(channel_id)].get('save_path', './recordings')
                            channel_name = data[int(channel_id)].get('channel_name', f'Channel {int(channel_id)+1}')
                            
                            logger.info(f"Searching in {save_path} for {channel_name}")
                            
                            # 創建錄影目錄如果不存在
                            os.makedirs(save_path, exist_ok=True)
                            
                            # 搜尋錄影檔案 - 支援多種格式和命名方式
                            search_patterns = [
                                f"{channel_name}_*.mp4",
                                f"*{channel_name}*.mp4", 
                                f"channel_{channel_id}_*.mp4",
                                "*.mp4"  # 所有mp4檔案
                            ]
                            
                            all_files = []
                            date_dir = os.path.join(save_path, date)
                            
                            # 先搜尋指定日期目錄
                            if os.path.exists(date_dir):
                                for pattern in search_patterns:
                                    files = glob.glob(os.path.join(date_dir, pattern))
                                    all_files.extend(files)
                            
                            # 如果沒找到，搜尋根目錄
                            if not all_files:
                                for pattern in search_patterns:
                                    files = glob.glob(os.path.join(save_path, pattern))
                                    all_files.extend(files)
                            
                            # 去重並排序
                            all_files = list(set(all_files))
                            all_files.sort()
                            
                            logger.info(f"Found {len(all_files)} files")
                            
                            for file_path in all_files:
                                try:
                                    file_stat = os.stat(file_path)
                                    recordings.append({
                                        'filename': os.path.basename(file_path),
                                        'path': file_path,
                                        'size': file_stat.st_size,
                                        'created': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                                        'duration': self.get_video_duration(file_path)
                                    })
                                except Exception as e:
                                    logger.error(f"Error processing file {file_path}: {e}")
                        else:
                            logger.error(f"Channel {channel_id} not found in settings")
                else:
                    logger.error("settings_free.json not found")
                
                return jsonify({'success': True, 'recordings': recordings})
            except Exception as e:
                logger.error(f"Error getting recordings: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/video/<path:filename>')
        def serve_video(filename):
            """提供影片檔案"""
            try:
                # 安全檢查檔案路徑
                if '..' in filename or filename.startswith('/'):
                    return jsonify({'error': 'Invalid file path'}), 400
                
                # 從錄影列表中找到完整路徑
                # 這裡需要根據實際的檔案結構來實現
                file_path = self.find_video_file(filename)
                
                if file_path and os.path.exists(file_path):
                    return send_file(file_path, mimetype='video/mp4')
                else:
                    return jsonify({'error': 'File not found'}), 404
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/live_stream/<int:channel_id>')
        def live_stream(channel_id):
            """即時串流 - 支援演示模式"""
            def generate_demo_frames():
                """生成演示幀"""
                import numpy as np
                from datetime import datetime
                
                frame_count = 0
                while True:
                    try:
                        # 創建演示圖片
                        img = np.zeros((240, 320, 3), dtype=np.uint8)
                        
                        # 根據頻道設定不同顏色
                        colors = [(100, 150, 200), (100, 200, 150), (150, 100, 200)]
                        color = colors[channel_id % len(colors)]
                        img[:] = color
                        
                        # 添加頻道資訊
                        cv2.putText(img, f"Channel {channel_id}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                        
                        # 添加時間戳
                        timestamp = datetime.now().strftime('%H:%M:%S')
                        cv2.putText(img, timestamp, (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                        
                        # 添加動態元素
                        x = int(160 + 50 * np.sin(frame_count * 0.1))
                        cv2.circle(img, (x, 120), 15, (255, 255, 255), -1)
                        
                        # 添加狀態
                        cv2.putText(img, "DEMO MODE", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                        cv2.putText(img, f"Frame: {frame_count}", (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
                        
                        # 編碼為JPEG
                        ret, buffer = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 70])
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        
                        frame_count += 1
                        time.sleep(0.3)  # 約3fps
                        
                    except Exception as e:
                        logger.error(f"Demo frame error: {e}")
                        break
            
            def generate_error_frames(channel_id, rtsp_url):
                """生成錯誤顯示幀"""
                import numpy as np
                from datetime import datetime
                
                while True:
                    try:
                        # 創建錯誤顯示圖片
                        img = np.zeros((240, 320, 3), dtype=np.uint8)
                        img[:] = (40, 40, 40)  # 深灰色背景
                        
                        # 添加錯誤圖標 (紅色X)
                        cv2.line(img, (140, 100), (180, 140), (0, 0, 255), 3)
                        cv2.line(img, (180, 100), (140, 140), (0, 0, 255), 3)
                        
                        # 添加錯誤訊息
                        cv2.putText(img, "Connection Failed", (80, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                        cv2.putText(img, f"Channel {channel_id}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                        
                        # 添加RTSP URL (截短)
                        if rtsp_url:
                            short_url = rtsp_url[:30] + "..." if len(rtsp_url) > 30 else rtsp_url
                            cv2.putText(img, short_url, (10, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (150, 150, 150), 1)
                        
                        # 添加時間戳
                        timestamp = datetime.now().strftime('%H:%M:%S')
                        cv2.putText(img, timestamp, (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                        
                        # 編碼為JPEG
                        ret, buffer = cv2.imencode('.jpg', img, [cv2.IMWRITE_JPEG_QUALITY, 70])
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        
                        time.sleep(1.0)  # 1fps，節省資源
                        
                    except Exception as e:
                        logger.error(f"Error frame generation failed: {e}")
                        break
            
            def generate_rtsp_frames():
                """生成RTSP串流幀"""
                try:
                    # 獲取頻道的RTSP URL
                    rtsp_url = self.get_channel_rtsp_url(channel_id)
                    if not rtsp_url:
                        logger.error(f"No RTSP URL for channel {channel_id}")
                        return
                    
                    logger.info(f"Starting RTSP stream for channel {channel_id}: {rtsp_url}")
                    
                    # 設定OpenCV參數
                    cap = cv2.VideoCapture(rtsp_url)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    cap.set(cv2.CAP_PROP_FPS, 8)
                    
                    if not cap.isOpened():
                        logger.error(f"Failed to open RTSP stream for channel {channel_id}")
                        return
                    
                    frame_count = 0
                    consecutive_failures = 0
                    
                    while consecutive_failures < 10:  # 最多允許10次連續失敗
                        ret, frame = cap.read()
                        if not ret:
                            consecutive_failures += 1
                            logger.warning(f"Failed to read frame from channel {channel_id} ({consecutive_failures}/10)")
                            time.sleep(0.5)
                            continue
                        
                        consecutive_failures = 0  # 重置失敗計數
                        
                        # 每3幀處理一次
                        frame_count += 1
                        if frame_count % 3 != 0:
                            continue
                        
                        try:
                            # 調整大小
                            frame = cv2.resize(frame, (320, 240))
                            
                            # 編碼為JPEG
                            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 50])
                            if ret:
                                frame_bytes = buffer.tobytes()
                                yield (b'--frame\r\n'
                                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        except Exception as e:
                            logger.error(f"Frame processing error: {e}")
                            continue
                        
                        time.sleep(0.2)
                    
                    cap.release()
                    logger.info(f"RTSP stream ended for channel {channel_id}")
                    
                except Exception as e:
                    logger.error(f"RTSP stream error for channel {channel_id}: {e}")
            
            # 獲取RTSP URL
            rtsp_url = self.get_channel_rtsp_url(channel_id)
            
            # 如果是demo://開頭，直接使用演示模式
            if rtsp_url and rtsp_url.startswith('demo://'):
                logger.info(f"Using demo mode for channel {channel_id}")
                return Response(generate_demo_frames(),
                              mimetype='multipart/x-mixed-replace; boundary=frame')
            
            # 嘗試RTSP連線，但有超時限制
            if rtsp_url:
                try:
                    logger.info(f"Testing RTSP connection for channel {channel_id}: {rtsp_url}")
                    test_cap = cv2.VideoCapture(rtsp_url)
                    test_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    if test_cap.isOpened():
                        # 嘗試讀取一幀，設定短超時
                        ret, frame = test_cap.read()
                        test_cap.release()
                        
                        if ret and frame is not None:
                            logger.info(f"RTSP connection successful for channel {channel_id}")
                            return Response(generate_rtsp_frames(),
                                          mimetype='multipart/x-mixed-replace; boundary=frame')
                        else:
                            logger.warning(f"RTSP connected but no frames for channel {channel_id}")
                    else:
                        logger.warning(f"Cannot open RTSP stream for channel {channel_id}")
                        
                except Exception as e:
                    logger.error(f"RTSP connection failed for channel {channel_id}: {e}")
            
            # RTSP失敗，使用錯誤顯示模式
            logger.info(f"Using error display mode for channel {channel_id}")
            return Response(generate_error_frames(channel_id, rtsp_url),
                          mimetype='multipart/x-mixed-replace; boundary=frame')
        
        @self.app.route('/api/test_stream/<int:channel_id>')
        def test_stream(channel_id):
            """測試串流連線"""
            try:
                rtsp_url = self.get_channel_rtsp_url(channel_id)
                if not rtsp_url:
                    return jsonify({'success': False, 'error': 'No RTSP URL found'})
                
                # 簡單測試連線
                cap = cv2.VideoCapture(rtsp_url)
                if cap.isOpened():
                    ret, frame = cap.read()
                    cap.release()
                    if ret:
                        return jsonify({'success': True, 'message': 'Stream connection OK'})
                    else:
                        return jsonify({'success': False, 'error': 'Cannot read frames'})
                else:
                    return jsonify({'success': False, 'error': 'Cannot connect to stream'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
    
    def setup_socketio(self):
        """設置WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info('Client connected')
            emit('status', {'message': 'Connected to NVRV-Free'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info('Client disconnected')
        
        @self.socketio.on('request_channel_status')
        def handle_channel_status():
            """發送頻道狀態更新"""
            status = self.get_all_channel_status()
            emit('channel_status_update', status)
    
    def get_video_duration(self, file_path):
        """獲取影片時長"""
        try:
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                return duration
            return 0
        except:
            return 0
    
    def find_video_file(self, filename):
        """查找影片檔案的完整路徑"""
        try:
            # 搜尋所有可能的錄影目錄
            if os.path.exists('settings_free.json'):
                with open('settings_free.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for channel_data in data:
                        save_path = channel_data.get('save_path', './recordings')
                        # 搜尋所有日期目錄
                        for date_dir in glob.glob(os.path.join(save_path, '*')):
                            if os.path.isdir(date_dir):
                                file_path = os.path.join(date_dir, filename)
                                if os.path.exists(file_path):
                                    return file_path
            return None
        except:
            return None
    
    def get_channel_rtsp_url(self, channel_id):
        """獲取頻道的RTSP URL"""
        try:
            if os.path.exists('settings_free.json'):
                with open('settings_free.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if channel_id < len(data):
                        return data[channel_id].get('rtsp_url', '')
            return ''
        except:
            return ''
    
    def get_all_channel_status(self):
        """獲取所有頻道狀態"""
        # TODO: 實現與NVRV主程式的狀態同步
        return {'channels': []}
    
    def start_recording(self, channel_id):
        """啟動錄影"""
        # TODO: 與NVRV主程式整合
        return f"Started recording for channel {channel_id}"
    
    def stop_recording(self, channel_id):
        """停止錄影"""
        # TODO: 與NVRV主程式整合
        return f"Stopped recording for channel {channel_id}"
    
    def run(self, host='0.0.0.0', port=8080, debug=False):
        """啟動Web服務器"""
        logger.info(f"Starting NVRV-Free Web Interface on http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)

# 獨立運行Web介面
if __name__ == '__main__':
    web_interface = NVRVWebInterface()
    web_interface.run(debug=True)