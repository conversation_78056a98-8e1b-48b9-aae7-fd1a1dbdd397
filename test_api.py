#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API測試腳本
"""

import requests
import json
import time

def test_channels_api():
    """測試頻道API"""
    print("🔍 測試頻道API...")
    try:
        response = requests.get('http://localhost:8080/api/channels', timeout=5)
        data = response.json()
        
        print(f"   狀態碼: {response.status_code}")
        print(f"   成功: {data.get('success', False)}")
        print(f"   頻道數量: {len(data.get('channels', []))}")
        
        for i, channel in enumerate(data.get('channels', [])):
            print(f"   頻道 {i}: {channel.get('name')} - {channel.get('status')}")
        
        return data.get('success', False)
    except Exception as e:
        print(f"   ❌ 錯誤: {e}")
        return False

def test_recordings_api():
    """測試錄影API"""
    print("🔍 測試錄影API...")
    try:
        # 先測試沒有參數的情況
        response = requests.get('http://localhost:8080/api/recordings', timeout=5)
        data = response.json()
        
        print(f"   狀態碼: {response.status_code}")
        print(f"   成功: {data.get('success', False)}")
        print(f"   錄影數量: {len(data.get('recordings', []))}")
        
        # 測試指定頻道
        response = requests.get('http://localhost:8080/api/recordings?channel_id=0', timeout=5)
        data = response.json()
        
        print(f"   頻道0錄影數量: {len(data.get('recordings', []))}")
        
        return data.get('success', False)
    except Exception as e:
        print(f"   ❌ 錯誤: {e}")
        return False

def test_stream_api():
    """測試串流API"""
    print("🔍 測試串流API...")
    try:
        # 只測試連線，不下載內容
        response = requests.head('http://localhost:8080/api/live_stream/0', timeout=10)
        
        print(f"   狀態碼: {response.status_code}")
        print(f"   內容類型: {response.headers.get('Content-Type', 'N/A')}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"   ❌ 錯誤: {e}")
        return False

def main():
    print("🧪 API測試工具")
    print("=" * 50)
    print("請確保Web介面已經啟動 (http://localhost:8080)")
    print()
    
    # 等待用戶確認
    input("按Enter開始測試...")
    
    # 測試各個API
    channels_ok = test_channels_api()
    time.sleep(1)
    
    recordings_ok = test_recordings_api()
    time.sleep(1)
    
    stream_ok = test_stream_api()
    
    print("\n📊 測試結果:")
    print("=" * 50)
    print(f"頻道API: {'✅ 正常' if channels_ok else '❌ 異常'}")
    print(f"錄影API: {'✅ 正常' if recordings_ok else '❌ 異常'}")
    print(f"串流API: {'✅ 正常' if stream_ok else '❌ 異常'}")
    
    if all([channels_ok, recordings_ok]):
        print("\n🎉 基本API功能正常！")
        print("如果Web介面仍有問題，請檢查瀏覽器開發者工具的Console頁面")
    else:
        print("\n⚠️  發現API問題，請檢查Web服務器日誌")

if __name__ == '__main__':
    main()