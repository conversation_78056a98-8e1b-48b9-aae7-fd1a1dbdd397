#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查打包環境是否準備就緒
"""

import sys
import os
import importlib

def check_module(module_name, description=""):
    """檢查模組是否可用"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {module_name} ({version}) - {description}")
        return True
    except ImportError:
        print(f"❌ {module_name} - 缺失 - {description}")
        return False

def check_file(file_path, description=""):
    """檢查檔案是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {file_path} - {description}")
        return True
    else:
        print(f"❌ {file_path} - 缺失 - {description}")
        return False

def main():
    print("NVRV-Free 打包環境檢查")
    print("=" * 40)
    
    print(f"Python版本: {sys.version}")
    print(f"作業系統: {os.name}")
    print()
    
    print("檢查必要模組:")
    modules_ok = True
    
    # 檢查核心模組
    modules_ok &= check_module('cv2', 'OpenCV - 影像處理核心')
    modules_ok &= check_module('PIL', 'Pillow - 圖像處理')
    modules_ok &= check_module('tkinter', 'GUI框架')
    modules_ok &= check_module('tkcalendar', '日曆控件')
    modules_ok &= check_module('babel', '國際化支援')
    modules_ok &= check_module('psutil', '系統監控')
    modules_ok &= check_module('pyinstaller', '打包工具')
    
    print()
    print("檢查檔案:")
    files_ok = True
    
    files_ok &= check_file('NVRV-free.py', '主程式檔案')
    files_ok &= check_file('640x360_功能說明.md', '說明文件')
    
    print()
    print("檢查OpenCV編碼器:")
    
    try:
        import cv2
        codecs_to_test = ['H264', 'avc1', 'X264', 'XVID', 'mp4v', 'MJPG']
        working_codecs = []
        
        for codec in codecs_to_test:
            try:
                fourcc = cv2.VideoWriter_fourcc(*codec)
                writer = cv2.VideoWriter('test_temp.mp4', fourcc, 1, (320, 240))
                if writer.isOpened():
                    print(f"✅ {codec} 編碼器可用")
                    working_codecs.append(codec)
                    writer.release()
                else:
                    print(f"❌ {codec} 編碼器不可用")
                    writer.release()
            except Exception as e:
                print(f"❌ {codec} 編碼器異常: {e}")
        
        # 清理測試檔案
        if os.path.exists('test_temp.mp4'):
            os.remove('test_temp.mp4')
            
        print(f"\n可用編碼器: {len(working_codecs)}/{len(codecs_to_test)}")
        
    except Exception as e:
        print(f"❌ OpenCV編碼器測試失敗: {e}")
    
    print()
    print("=" * 40)
    
    if modules_ok and files_ok:
        print("✅ 環境檢查通過！可以開始打包")
        print("\n建議的打包命令:")
        print("1. 簡單打包: build_simple.bat")
        print("2. 完整打包: build_exe.bat")
    else:
        print("❌ 環境檢查失敗！請先解決以上問題")
        print("\n解決方案:")
        if not modules_ok:
            print("- 安裝缺失的模組: pip install -r requirements.txt")
        if not files_ok:
            print("- 確保所有必要檔案都在當前目錄")

if __name__ == "__main__":
    main()