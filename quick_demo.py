#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示Web介面
"""

import json
import os
from web_interface import NVRVWebInterface

def main():
    print("快速演示模式啟動...")
    
    # 創建演示設定
    demo_settings = [
        {
            "channel_name": "演示頻道1",
            "rtsp_url": "demo://channel1",
            "save_path": "./recordings"
        },
        {
            "channel_name": "演示頻道2",
            "rtsp_url": "demo://channel2", 
            "save_path": "./recordings"
        },
        {
            "channel_name": "演示頻道3",
            "rtsp_url": "demo://channel3",
            "save_path": "./recordings"
        }
    ]
    
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(demo_settings, f, ensure_ascii=False, indent=2)
    
    os.makedirs('./recordings', exist_ok=True)
    
    print("網址: http://localhost:8082")
    print("演示頻道已設置，按 Ctrl+C 停止")
    
    try:
        web_interface = NVRVWebInterface()
        web_interface.run(host='127.0.0.1', port=8082, debug=False)
    except KeyboardInterrupt:
        print("演示結束")

if __name__ == '__main__':
    main()