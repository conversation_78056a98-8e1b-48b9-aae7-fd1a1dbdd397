#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
編碼器診斷工具 - 用於檢測工作機器上的編碼器支援情況
可以打包進EXE中，在工作機器上運行診斷
"""

import cv2
import numpy as np
import os
import sys
import platform
from datetime import datetime

def log_info(message):
    """記錄診斷信息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")
    
    # 同時寫入日誌文件
    with open("codec_diagnostic.log", "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")

def test_codec_simple(codec_name):
    """簡單測試編碼器是否可用"""
    try:
        fourcc = cv2.VideoWriter_fourcc(*codec_name)
        # 創建一個最小的測試
        writer = cv2.VideoWriter("test_temp.mp4", fourcc, 1, (320, 240))
        is_opened = writer.isOpened()
        writer.release()
        
        # 清理測試文件
        if os.path.exists("test_temp.mp4"):
            os.remove("test_temp.mp4")
            
        return is_opened
    except Exception as e:
        log_info(f"編碼器 {codec_name} 測試異常: {e}")
        return False

def test_codec_full(codec_name, codec_desc):
    """完整測試編碼器功能"""
    try:
        width, height = 640, 360
        fps = 8
        test_filename = f"diagnostic_{codec_name}.mp4"
        
        fourcc = cv2.VideoWriter_fourcc(*codec_name)
        writer = cv2.VideoWriter(test_filename, fourcc, fps, (width, height))
        
        if writer.isOpened():
            # 創建測試幀
            test_frame = np.zeros((height, width, 3), dtype=np.uint8)
            test_frame[:] = (0, 100, 200)  # 藍色背景
            
            # 寫入測試幀
            success_count = 0
            for i in range(5):
                try:
                    cv2.putText(test_frame, f"Test {i}", (50, 100), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    writer.write(test_frame)
                    success_count += 1
                except Exception as e:
                    log_info(f"寫入幀 {i} 失敗: {e}")
                    break
            
            writer.release()
            
            # 檢查結果
            if os.path.exists(test_filename):
                file_size = os.path.getsize(test_filename)
                if file_size > 0:
                    log_info(f"✅ {codec_desc} - 測試成功 (檔案大小: {file_size} bytes, 成功幀數: {success_count}/5)")
                    os.remove(test_filename)
                    return True, success_count
                else:
                    log_info(f"❌ {codec_desc} - 檔案大小為0")
                    os.remove(test_filename)
                    return False, 0
            else:
                log_info(f"❌ {codec_desc} - 檔案未創建")
                return False, 0
        else:
            writer.release()
            log_info(f"❌ {codec_desc} - VideoWriter無法開啟")
            return False, 0
            
    except Exception as e:
        log_info(f"❌ {codec_desc} - 測試異常: {e}")
        return False, 0

def check_system_info():
    """檢查系統信息"""
    log_info("=== 系統信息 ===")
    log_info(f"作業系統: {platform.system()} {platform.release()}")
    log_info(f"架構: {platform.architecture()[0]}")
    log_info(f"處理器: {platform.processor()}")
    log_info(f"Python版本: {sys.version}")
    log_info(f"OpenCV版本: {cv2.__version__}")
    
    # 檢查OpenCV編譯信息
    build_info = cv2.getBuildInformation()
    log_info("OpenCV編譯信息:")
    for line in build_info.split('\n'):
        if any(keyword in line.lower() for keyword in ['ffmpeg', 'codec', 'video']):
            log_info(f"  {line.strip()}")

def main():
    """主診斷程序"""
    print("NVRV-Free 編碼器診斷工具")
    print("=" * 50)
    
    # 清理舊的日誌文件
    if os.path.exists("codec_diagnostic.log"):
        os.remove("codec_diagnostic.log")
    
    log_info("開始編碼器診斷...")
    
    # 檢查系統信息
    check_system_info()
    
    log_info("\n=== 編碼器測試 ===")
    
    # 要測試的編碼器（按優先順序）
    codecs_to_test = [
        ('XVID', 'XVID編碼器 (推薦)'),
        ('mp4v', 'MP4V編碼器'),
        ('MJPG', 'MJPG編碼器'),
        ('X264', 'X264編碼器'),
        ('avc1', 'AVC1編碼器'),
        ('H264', 'H264編碼器')
    ]
    
    working_codecs = []
    failed_codecs = []
    
    for codec_name, codec_desc in codecs_to_test:
        log_info(f"\n測試 {codec_desc}...")
        
        # 先做簡單測試
        if test_codec_simple(codec_name):
            # 再做完整測試
            success, frame_count = test_codec_full(codec_name, codec_desc)
            if success:
                working_codecs.append((codec_name, codec_desc, frame_count))
            else:
                failed_codecs.append((codec_name, codec_desc, "完整測試失敗"))
        else:
            failed_codecs.append((codec_name, codec_desc, "簡單測試失敗"))
    
    # 輸出結果摘要
    log_info("\n" + "=" * 50)
    log_info("診斷結果摘要:")
    
    if working_codecs:
        log_info(f"\n✅ 可用編碼器 ({len(working_codecs)}/{len(codecs_to_test)}):")
        for codec_name, codec_desc, frame_count in working_codecs:
            log_info(f"   - {codec_desc} ({codec_name}) - 幀數: {frame_count}/5")
        
        recommended = working_codecs[0]
        log_info(f"\n🎯 建議使用: {recommended[1]} ({recommended[0]})")
        log_info("   NVRV-Free會自動選擇此編碼器")
        
    else:
        log_info("\n❌ 沒有可用的編碼器！")
        log_info("   請安裝以下組件:")
        log_info("   1. Visual C++ Redistributable")
        log_info("   2. 重新安裝OpenCV")
        log_info("   3. 檢查FFMPEG支援")
    
    if failed_codecs:
        log_info(f"\n⚠️  失敗的編碼器 ({len(failed_codecs)}):")
        for codec_name, codec_desc, reason in failed_codecs:
            log_info(f"   - {codec_desc} ({codec_name}): {reason}")
    
    log_info(f"\n📄 詳細日誌已保存至: codec_diagnostic.log")
    log_info("診斷完成！")
    
    # 如果是EXE環境，等待用戶按鍵
    if getattr(sys, 'frozen', False):
        input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()