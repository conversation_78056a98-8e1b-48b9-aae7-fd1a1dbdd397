#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試快速載入的Web介面
"""

import requests
import time
import threading
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8084, debug=False)

def test_fast_loading():
    """測試快速載入"""
    print("🚀 啟動快速載入測試...")
    
    # 在背景啟動服務器
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服務器啟動
    time.sleep(2)
    
    try:
        # 測試頻道API載入速度
        print("⏱️  測試頻道API載入速度...")
        start_time = time.time()
        
        response = requests.get('http://127.0.0.1:8084/api/channels', timeout=10)
        
        end_time = time.time()
        load_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"✅ 頻道API載入成功")
                print(f"   載入時間: {load_time:.2f} 秒")
                print(f"   頻道數量: {len(channels)}")
                
                # 統計狀態
                status_counts = {}
                for channel in channels:
                    status = channel.get('status', 'unknown')
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                print(f"   狀態統計:")
                for status, count in status_counts.items():
                    status_icon = {
                        'ready': '⚪',
                        'offline': '🔴',
                        'demo': '🟡',
                        'online': '🟢'
                    }.get(status, '❓')
                    print(f"      {status_icon} {status}: {count}")
                
                # 測試單一頻道連線
                if channels:
                    first_channel = channels[0]
                    if first_channel.get('rtsp_url'):
                        print(f"\n🔍 測試單一頻道連線...")
                        channel_id = first_channel.get('id')
                        channel_name = first_channel.get('name')
                        
                        print(f"   測試頻道: {channel_name}")
                        
                        test_start = time.time()
                        test_response = requests.get(f'http://127.0.0.1:8084/api/test_stream/{channel_id}', timeout=10)
                        test_end = time.time()
                        test_time = test_end - test_start
                        
                        if test_response.status_code == 200:
                            test_data = test_response.json()
                            if test_data.get('success'):
                                print(f"   ✅ 連線測試成功 ({test_time:.2f}秒)")
                                print(f"   訊息: {test_data.get('message')}")
                            else:
                                print(f"   ❌ 連線測試失敗 ({test_time:.2f}秒)")
                                print(f"   錯誤: {test_data.get('error')}")
                        else:
                            print(f"   ❌ 測試API錯誤: {test_response.status_code}")
                
                print(f"\n📊 效能總結:")
                print(f"   ✅ 頁面載入不再被RTSP連線阻塞")
                print(f"   ✅ 頻道列表快速載入 ({load_time:.2f}秒)")
                print(f"   ✅ 可以立即使用其他功能")
                print(f"   ✅ RTSP連線測試獨立進行")
                
            else:
                print(f"❌ API錯誤: {data.get('error')}")
        else:
            print(f"❌ HTTP錯誤: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 連接失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == '__main__':
    test_fast_loading()