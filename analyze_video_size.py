#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影片檔案大小分析工具
用於診斷錄影檔案為什麼過大
"""

import cv2
import os
import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_video_file(file_path):
    """分析影片檔案"""
    if not os.path.exists(file_path):
        print(f"❌ 檔案不存在: {file_path}")
        return
    
    print(f"📁 分析檔案: {file_path}")
    print("=" * 50)
    
    # 檔案基本信息
    file_size = os.path.getsize(file_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"檔案大小: {file_size_mb:.2f} MB ({file_size:,} bytes)")
    
    # 開啟影片
    cap = cv2.VideoCapture(file_path)
    if not cap.isOpened():
        print("❌ 無法開啟影片檔案")
        return
    
    # 獲取影片屬性
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # 計算時長
    duration_seconds = frame_count / fps if fps > 0 else 0
    duration_minutes = duration_seconds / 60
    
    print(f"解析度: {width}x{height}")
    print(f"FPS: {fps:.2f}")
    print(f"總幀數: {frame_count:,}")
    print(f"時長: {duration_minutes:.2f} 分鐘 ({duration_seconds:.1f} 秒)")
    
    # 計算位元率
    if duration_seconds > 0:
        bitrate_bps = (file_size * 8) / duration_seconds
        bitrate_kbps = bitrate_bps / 1000
        bitrate_mbps = bitrate_kbps / 1000
        
        print(f"平均位元率: {bitrate_kbps:.0f} kbps ({bitrate_mbps:.2f} Mbps)")
    
    # 每幀平均大小
    if frame_count > 0:
        bytes_per_frame = file_size / frame_count
        print(f"每幀平均大小: {bytes_per_frame:.0f} bytes")
    
    # 每分鐘檔案大小
    if duration_minutes > 0:
        mb_per_minute = file_size_mb / duration_minutes
        print(f"每分鐘檔案大小: {mb_per_minute:.2f} MB")
    
    print()
    print("📊 大小評估:")
    
    # 評估檔案大小是否合理
    expected_sizes = {
        "H.264 (640x360, 8fps)": (1.5, 2.5),  # MB per minute
        "XVID (640x360, 8fps)": (2.5, 4.0),
        "MJPG (640x360, 8fps)": (5.0, 8.0),
    }
    
    if duration_minutes > 0:
        actual_mb_per_min = file_size_mb / duration_minutes
        
        for codec_desc, (min_size, max_size) in expected_sizes.items():
            if min_size <= actual_mb_per_min <= max_size:
                print(f"✅ 符合 {codec_desc} 預期大小")
            elif actual_mb_per_min > max_size:
                excess = actual_mb_per_min - max_size
                print(f"⚠️  超過 {codec_desc} 預期大小 {excess:.1f} MB/分鐘")
    
    # 建議
    print()
    print("💡 優化建議:")
    
    if width > 640 or height > 360:
        print(f"- 解析度過高 ({width}x{height})，建議強制轉換為640x360")
    
    if fps > 10:
        print(f"- FPS過高 ({fps:.1f})，建議降低到8fps")
    
    if duration_minutes > 0:
        actual_mb_per_min = file_size_mb / duration_minutes
        if actual_mb_per_min > 4:
            print(f"- 檔案過大 ({actual_mb_per_min:.1f} MB/分鐘)，可能使用了MJPG編碼器")
            print("- 建議使用H.264或XVID編碼器")
            print("- 降低品質參數到30或更低")
    
    cap.release()

def main():
    print("NVRV-Free 影片檔案大小分析工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        # 從命令列參數獲取檔案路徑
        file_path = sys.argv[1]
        analyze_video_file(file_path)
    else:
        # 互動式選擇檔案
        print("請輸入影片檔案路徑 (或拖拽檔案到此視窗):")
        file_path = input().strip().strip('"')
        
        if file_path:
            analyze_video_file(file_path)
        else:
            print("❌ 未提供檔案路徑")
    
    print()
    input("按Enter鍵退出...")

if __name__ == "__main__":
    main()