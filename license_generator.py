#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
軟體授權工具
用於生成機器授權檔案
"""

import hashlib
import tkinter as tk
from tkinter import messagebox, filedialog
import os

class LicenseGenerator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("軟體授權工具")
        self.root.geometry("500x400")
        self.root.configure(bg="#ECEFF1")
        self.root.resizable(False, False)
        
        self.setup_ui()
        
    def setup_ui(self):
        # 標題
        title_label = tk.Label(self.root, text="NVRV-Free - 授權工具",
                              font=("Roboto", 16, "bold"),
                              bg="#ECEFF1", fg="#2E86AB")
        title_label.pack(pady=20)
        
        # 說明
        info_text = """此工具用於生成NVRV-Free軟體授權檔案
請輸入需要授權的機器ID，然後生成授權檔案
將生成的 machine.id 檔案複製到NVRV-Free軟體目錄即可"""
        
        info_label = tk.Label(self.root, text=info_text, 
                             font=("Roboto", 10), 
                             bg="#ECEFF1", fg="#333333",
                             justify=tk.LEFT)
        info_label.pack(pady=10, padx=20)
        
        # 輸入框架
        input_frame = tk.Frame(self.root, bg="#FFFFFF", relief=tk.RAISED, bd=2)
        input_frame.pack(pady=20, padx=50, fill=tk.X)
        
        # 機器ID輸入
        tk.Label(input_frame, text="機器ID:", font=("Roboto", 12), 
                bg="#FFFFFF", fg="#333333").pack(pady=(20, 5))
        
        self.machine_id_entry = tk.Entry(input_frame, font=("Roboto", 12), 
                                        width=50, bg="#F5F5F5", fg="#333333")
        self.machine_id_entry.pack(pady=5, padx=20)
        
        # 按鈕框架
        button_frame = tk.Frame(input_frame, bg="#FFFFFF")
        button_frame.pack(pady=20)
        
        # 生成授權檔案按鈕
        generate_btn = tk.Button(button_frame, text="生成授權檔案", 
                               font=("Roboto", 12, "bold"),
                               bg="#4CAF50", fg="white", width=15,
                               command=self.generate_license, 
                               relief=tk.FLAT, bd=0)
        generate_btn.pack(side=tk.LEFT, padx=10)
        
        # 驗證授權檔案按鈕
        verify_btn = tk.Button(button_frame, text="驗證授權檔案", 
                              font=("Roboto", 12, "bold"),
                              bg="#2196F3", fg="white", width=15,
                              command=self.verify_license, 
                              relief=tk.FLAT, bd=0)
        verify_btn.pack(side=tk.LEFT, padx=10)
        
        # 結果顯示區域
        result_frame = tk.Frame(self.root, bg="#ECEFF1")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        tk.Label(result_frame, text="操作結果:", font=("Roboto", 12, "bold"), 
                bg="#ECEFF1", fg="#333333").pack(anchor=tk.W)
        
        self.result_text = tk.Text(result_frame, height=8, width=60,
                                  font=("Consolas", 10), bg="#F5F5F5", fg="#333333",
                                  wrap=tk.WORD, state=tk.DISABLED)
        self.result_text.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 滾動條
        scrollbar = tk.Scrollbar(result_frame, command=self.result_text.yview)
        self.result_text.config(yscrollcommand=scrollbar.set)
        
    def log_result(self, message):
        """在結果區域顯示訊息"""
        self.result_text.config(state=tk.NORMAL)
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.result_text.config(state=tk.DISABLED)
        
    def generate_license(self):
        """生成授權檔案"""
        machine_id = self.machine_id_entry.get().strip()
        
        if not machine_id:
            messagebox.showerror("錯誤", "請輸入機器ID")
            return
            
        # 驗證機器ID格式（應該是32位MD5）
        if len(machine_id) != 32 or not all(c in '0123456789abcdef' for c in machine_id.lower()):
            messagebox.showerror("錯誤", "機器ID格式錯誤，應該是32位十六進制字符串")
            return
            
        try:
            # 生成簽名 (使用與NVRV-Free相同的密鑰)
            signature = hashlib.md5(f"NVRV_FREE_2024_{machine_id}_2024".encode()).hexdigest()
            
            # 生成授權內容
            license_content = f"{machine_id}|{signature}"
            
            # 選擇保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存授權檔案",
                defaultextension=".id",
                filetypes=[("授權檔案", "*.id"), ("所有檔案", "*.*")],
                initialfile="machine.id"
            )
            
            if file_path:
                with open(file_path, 'w') as f:
                    f.write(license_content)
                
                self.log_result(f"✓ 授權檔案生成成功")
                self.log_result(f"  檔案位置: {file_path}")
                self.log_result(f"  機器ID: {machine_id}")
                self.log_result(f"  簽名: {signature}")
                self.log_result(f"  請將此檔案複製到軟體目錄")
                self.log_result("-" * 50)
                
                messagebox.showinfo("成功", f"授權檔案已生成：\n{file_path}")
            
        except Exception as e:
            error_msg = f"生成授權檔案失敗: {str(e)}"
            self.log_result(f"✗ {error_msg}")
            messagebox.showerror("錯誤", error_msg)
    
    def verify_license(self):
        """驗證授權檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇授權檔案",
            filetypes=[("授權檔案", "*.id"), ("所有檔案", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'r') as f:
                content = f.read().strip()
            
            if '|' not in content:
                self.log_result(f"✗ 授權檔案格式錯誤: {file_path}")
                messagebox.showerror("錯誤", "授權檔案格式錯誤")
                return
            
            machine_id, signature = content.split('|', 1)
            
            # 驗證簽名 (使用與NVRV-Free相同的密鑰)
            expected_signature = hashlib.md5(f"NVRV_FREE_2024_{machine_id}_2024".encode()).hexdigest()
            
            if signature == expected_signature:
                self.log_result(f"✓ 授權檔案驗證成功")
                self.log_result(f"  檔案位置: {file_path}")
                self.log_result(f"  授權機器ID: {machine_id}")
                self.log_result(f"  簽名驗證: 通過")
                self.log_result("-" * 50)
                messagebox.showinfo("驗證成功", f"授權檔案有效\n授權機器ID: {machine_id}")
            else:
                self.log_result(f"✗ 授權檔案簽名無效: {file_path}")
                self.log_result(f"  機器ID: {machine_id}")
                self.log_result(f"  檔案簽名: {signature}")
                self.log_result(f"  期望簽名: {expected_signature}")
                self.log_result("-" * 50)
                messagebox.showerror("驗證失敗", "授權檔案簽名無效")
                
        except Exception as e:
            error_msg = f"驗證授權檔案失敗: {str(e)}"
            self.log_result(f"✗ {error_msg}")
            messagebox.showerror("錯誤", error_msg)
    
    def run(self):
        """運行授權工具"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LicenseGenerator()
    app.run()
