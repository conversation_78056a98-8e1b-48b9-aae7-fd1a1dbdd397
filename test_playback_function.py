#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試播放功能
"""

import requests
import time
import threading
import os
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8087, debug=False)

def create_test_video():
    """創建測試影片檔案"""
    import cv2
    import numpy as np
    from datetime import datetime
    
    # 選擇第一個頻道的儲存路徑
    try:
        import json
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if config:
            first_channel = config[0]
            save_path = first_channel.get('save_path', './recordings')
            channel_name = first_channel.get('channel_name', 'Test Channel')
            
            # 創建目錄
            os.makedirs(save_path, exist_ok=True)
            
            # 創建測試影片
            filename = f"test_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
            output_path = os.path.join(save_path, filename)
            
            print(f"🎬 創建測試影片: {output_path}")
            
            # 影片參數
            width, height = 640, 480
            fps = 8
            duration = 10  # 10秒
            total_frames = duration * fps
            
            # 創建VideoWriter
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            if not out.isOpened():
                print(f"❌ 無法創建影片檔案")
                return None, None
            
            # 生成測試幀
            for frame_num in range(total_frames):
                # 創建彩色背景
                hue = (frame_num * 5) % 180
                img = np.full((height, width, 3), (hue, 255, 255), dtype=np.uint8)
                img = cv2.cvtColor(img, cv2.COLOR_HSV2BGR)
                
                # 添加文字
                cv2.putText(img, f"Test Video - {channel_name}", (20, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(img, f"Frame: {frame_num+1}/{total_frames}", (20, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # 添加動態圓圈
                center_x = width // 2 + int(100 * np.sin(frame_num * 0.2))
                center_y = height // 2 + int(50 * np.cos(frame_num * 0.2))
                cv2.circle(img, (center_x, center_y), 30, (0, 255, 0), -1)
                
                out.write(img)
            
            out.release()
            
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                file_size = os.path.getsize(output_path) / (1024 * 1024)
                print(f"✅ 測試影片創建成功: {file_size:.2f} MB")
                return filename, output_path
            else:
                print(f"❌ 測試影片創建失敗")
                return None, None
                
    except Exception as e:
        print(f"❌ 創建測試影片時發生錯誤: {e}")
        return None, None

def main():
    print("=" * 60)
    print("NVRV-Free 播放功能測試")
    print("=" * 60)
    
    # 1. 創建測試影片
    print("🎬 準備測試影片...")
    test_filename, test_path = create_test_video()
    
    if not test_filename:
        print("❌ 無法創建測試影片，跳過播放測試")
        return
    
    # 2. 啟動服務器
    print("\n🚀 啟動Web服務器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(3)
    
    try:
        # 3. 測試頻道API
        print("\n📋 測試頻道API...")
        response = requests.get('http://127.0.0.1:8087/api/channels', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"✅ 載入 {len(channels)} 個頻道")
                
                if channels:
                    first_channel = channels[0]
                    channel_id = first_channel.get('id', 0)
                    channel_name = first_channel.get('name', 'Unknown')
                    print(f"   測試頻道: {channel_name}")
                else:
                    print("❌ 沒有頻道可測試")
                    return
            else:
                print(f"❌ 頻道API錯誤: {data.get('error')}")
                return
        else:
            print(f"❌ 頻道API HTTP錯誤: {response.status_code}")
            return
        
        # 4. 測試錄影API
        print(f"\n📹 測試錄影API...")
        today = time.strftime('%Y%m%d')
        response = requests.get(f'http://127.0.0.1:8087/api/recordings?channel_id={channel_id}&date={today}', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                recordings = data.get('recordings', [])
                print(f"✅ 找到 {len(recordings)} 個錄影檔案")
                
                # 查找我們的測試影片
                test_recording = None
                for recording in recordings:
                    if recording.get('filename') == test_filename:
                        test_recording = recording
                        break
                
                if test_recording:
                    print(f"✅ 找到測試影片: {test_filename}")
                    print(f"   檔案大小: {test_recording.get('size', 0) / (1024*1024):.2f} MB")
                    print(f"   時長: {test_recording.get('duration', 0):.1f} 秒")
                else:
                    print(f"⚠️  測試影片未在錄影列表中找到")
                    print(f"   可能需要等待檔案掃描完成")
            else:
                print(f"❌ 錄影API錯誤: {data.get('error')}")
        else:
            print(f"❌ 錄影API HTTP錯誤: {response.status_code}")
        
        # 5. 測試影片服務
        print(f"\n🎥 測試影片服務...")
        video_url = f'http://127.0.0.1:8087/api/video/{test_filename}'
        print(f"   影片URL: {video_url}")
        
        response = requests.head(video_url, timeout=10)
        
        if response.status_code == 200:
            content_length = response.headers.get('Content-Length', '0')
            content_type = response.headers.get('Content-Type', 'unknown')
            
            print(f"✅ 影片服務正常")
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Length: {int(content_length) / (1024*1024):.2f} MB")
            
            # 測試實際下載一小部分
            response = requests.get(video_url, timeout=10, stream=True, 
                                  headers={'Range': 'bytes=0-1023'})
            
            if response.status_code in [200, 206]:
                print(f"✅ 影片內容可正常讀取")
            else:
                print(f"⚠️  影片內容讀取異常: {response.status_code}")
                
        else:
            print(f"❌ 影片服務錯誤: {response.status_code}")
            if response.status_code == 404:
                print(f"   影片檔案未找到，檢查檔案路徑...")
        
        # 6. 測試歷史回放頁面
        print(f"\n🌐 測試歷史回放頁面...")
        response = requests.get('http://127.0.0.1:8087/playback', timeout=5)
        
        if response.status_code == 200:
            print(f"✅ 歷史回放頁面載入成功")
            
            content = response.text
            if 'playVideo' in content:
                print(f"✅ 包含播放功能")
            if 'videojs' in content:
                print(f"✅ 包含Video.js播放器")
        else:
            print(f"❌ 歷史回放頁面載入失敗: {response.status_code}")
        
        print(f"\n" + "=" * 60)
        print("✅ 播放功能測試完成")
        print(f"\n📋 測試結果總結:")
        print(f"   ✅ 移除了系統設定功能")
        print(f"   ✅ 改進了播放按鈕的錯誤處理")
        print(f"   ✅ 添加了詳細的影片服務日誌")
        print(f"   ✅ 影片檔案搜尋功能正常")
        
        print(f"\n🌐 Web介面已就緒:")
        print(f"   http://localhost:8080")
        print(f"   - 即時監控: 列表模式，按需預覽")
        print(f"   - 歷史回放: 改進的播放功能")
        print(f"   - 已移除系統設定頁面")
        
        print(f"\n🎬 測試影片位置:")
        print(f"   {test_path}")
        print(f"   可在歷史回放頁面中測試播放功能")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    
    finally:
        # 清理測試檔案
        if test_path and os.path.exists(test_path):
            try:
                os.remove(test_path)
                print(f"\n🧹 已清理測試影片檔案")
            except:
                print(f"\n⚠️  無法清理測試影片檔案: {test_path}")

if __name__ == '__main__':
    main()