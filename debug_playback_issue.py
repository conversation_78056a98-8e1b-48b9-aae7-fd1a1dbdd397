#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試歷史回放按鈕問題
"""

import requests
import time
import threading
import webbrowser
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8089, debug=False)

def main():
    print("=" * 60)
    print("NVRV-Free 歷史回放按鈕問題調試")
    print("=" * 60)
    
    # 啟動服務器
    print("🚀 啟動Web服務器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(3)
    
    try:
        # 檢查服務器是否正常運行
        print("\n🔍 檢查服務器狀態...")
        response = requests.get('http://127.0.0.1:8089/', timeout=5)
        
        if response.status_code != 200:
            print(f"❌ 服務器無法訪問: {response.status_code}")
            return
        
        print("✅ 服務器運行正常")
        
        # 檢查JavaScript代碼
        print("\n📝 檢查JavaScript代碼...")
        content = response.text
        
        # 檢查函數定義
        functions_to_check = [
            ('goToPlayback', 'function goToPlayback('),
            ('goToChannelPlayback', 'function goToChannelPlayback('),
            ('previewChannel', 'function previewChannel('),
            ('loadChannels', 'function loadChannels(')
        ]
        
        for func_name, func_signature in functions_to_check:
            if func_signature in content:
                print(f"✅ {func_name} 函數已定義")
            else:
                print(f"❌ {func_name} 函數未找到")
        
        # 檢查按鈕HTML
        print("\n🔘 檢查按鈕HTML...")
        button_patterns = [
            ('列表模式歷史回放按鈕', 'onclick="console.log(\'History button clicked'),
            ('網格模式歷史回放按鈕', 'onclick="console.log(\'Grid history button clicked'),
            ('預覽模式歷史回放按鈕', 'onclick="console.log(\'Preview modal history button clicked')
        ]
        
        for button_name, pattern in button_patterns:
            if pattern in content:
                print(f"✅ {button_name} 存在且包含調試代碼")
            else:
                print(f"❌ {button_name} 不存在或缺少調試代碼")
        
        # 檢查可能的JavaScript錯誤
        print("\n⚠️  檢查可能的問題...")
        
        # 檢查是否有語法錯誤的跡象
        if 'SyntaxError' in content:
            print("❌ 發現可能的JavaScript語法錯誤")
        else:
            print("✅ 沒有明顯的語法錯誤")
        
        # 檢查Bootstrap和其他依賴
        if 'bootstrap@5.1.3' in content:
            print("✅ Bootstrap 5.1.3 已載入")
        else:
            print("⚠️  Bootstrap版本可能不正確")
        
        if 'font-awesome' in content:
            print("✅ Font Awesome 已載入")
        else:
            print("⚠️  Font Awesome 可能未載入")
        
        # 提供調試建議
        print(f"\n" + "=" * 60)
        print("🔧 調試建議:")
        print("1. 在瀏覽器中打開: http://localhost:8080")
        print("2. 按F12打開開發者工具")
        print("3. 切換到Console標籤")
        print("4. 重新載入頁面，查看是否有JavaScript錯誤")
        print("5. 點擊歷史回放按鈕，查看控制台輸出")
        print("6. 如果按鈕沒有反應，檢查:")
        print("   - 是否有JavaScript錯誤阻止執行")
        print("   - 按鈕是否被其他元素覆蓋")
        print("   - 事件是否正確綁定")
        
        print(f"\n🧪 手動測試步驟:")
        print("1. 在瀏覽器控制台中輸入: goToPlayback(0)")
        print("2. 應該看到調試輸出並跳轉到歷史回放頁面")
        print("3. 如果沒有跳轉，檢查控制台錯誤訊息")
        
        print(f"\n📋 常見問題和解決方案:")
        print("• 按鈕沒有反應 → 檢查JavaScript錯誤")
        print("• 函數未定義錯誤 → 檢查腳本載入順序")
        print("• 頁面跳轉失敗 → 檢查URL格式和路由")
        print("• 點擊事件無效 → 檢查DOM元素是否正確生成")
        
        # 自動打開瀏覽器進行測試
        print(f"\n🌐 自動打開瀏覽器進行測試...")
        try:
            webbrowser.open('http://127.0.0.1:8089/')
            print("✅ 瀏覽器已打開，請手動測試按鈕功能")
        except Exception as e:
            print(f"⚠️  無法自動打開瀏覽器: {e}")
            print("請手動打開: http://127.0.0.1:8089/")
        
        print(f"\n⏳ 服務器將保持運行，按Ctrl+C停止...")
        
        # 保持服務器運行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 服務器已停止")
        
    except Exception as e:
        print(f"❌ 調試過程中發生錯誤: {e}")

if __name__ == '__main__':
    main()