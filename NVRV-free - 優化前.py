import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import os
from datetime import datetime, timedelta
import json
import time
import logging
import gc
from tkcalendar import Calendar
import babel.numbers
import babel.dates
import webbrowser  # 用於打開網頁連結
import hashlib
import platform
import sys

# 機器授權驗證類
class MachineAuthorization:
    """機器授權驗證類"""

    def __init__(self, license_file="machine.id"):
        """
        初始化授權驗證

        Args:
            license_file (str): 授權檔案名稱，預設為 machine.id
        """
        self.license_file = license_file
        self.signature_key = "NVRV_FREE_2024"  # 專用密鑰

    def get_machine_id(self):
        """
        獲取機器唯一ID

        Returns:
            str: 32位MD5機器ID
        """
        try:
            # 組合多個硬體信息生成唯一ID
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            return hashlib.md5(machine_info.encode()).hexdigest()
        except:
            # 如果獲取硬體信息失敗，使用備用方案
            import uuid
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()

    def generate_signature(self, machine_id):
        """
        生成授權簽名

        Args:
            machine_id (str): 機器ID

        Returns:
            str: 授權簽名
        """
        return hashlib.md5(f"{self.signature_key}_{machine_id}_2024".encode()).hexdigest()

    def show_authorization_error(self, current_machine_id, authorized_id="", error_type=""):
        """
        顯示授權錯誤對話框，包含複製機器ID功能

        Args:
            current_machine_id (str): 當前機器ID
            authorized_id (str): 授權的機器ID
            error_type (str): 錯誤類型
        """
        # 創建自定義對話框
        dialog = tk.Toplevel()
        dialog.title("NVRV-Free 授權錯誤")
        dialog.geometry("500x350")
        dialog.configure(bg="#ECEFF1")
        dialog.resizable(False, False)
        dialog.grab_set()  # 模態對話框

        # 居中顯示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (350 // 2)
        dialog.geometry(f"500x350+{x}+{y}")

        # 錯誤圖標和標題
        title_frame = tk.Frame(dialog, bg="#ECEFF1")
        title_frame.pack(pady=20)

        tk.Label(title_frame, text="⚠️", font=("Arial", 32), bg="#ECEFF1", fg="#FF5722").pack()
        tk.Label(title_frame, text="NVRV-Free 軟體授權錯誤", font=("Roboto", 16, "bold"),
                bg="#ECEFF1", fg="#D32F2F").pack(pady=5)

        # 錯誤信息
        info_frame = tk.Frame(dialog, bg="#FFFFFF", relief=tk.RAISED, bd=2)
        info_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        if error_type == "機器ID不匹配":
            error_msg = f"此軟體未授權在此機器上運行！\n\n當前機器ID與授權ID不匹配。"
        elif error_type == "授權檔案簽名無效":
            error_msg = f"授權檔案簽名無效！\n\n檔案可能被篡改或損壞。"
        elif error_type == "未找到授權檔案":
            error_msg = f"未找到授權檔案！\n\n此軟體需要有效的授權檔案才能運行。"
        else:
            error_msg = f"授權驗證失敗！\n\n{error_type}"

        tk.Label(info_frame, text=error_msg, font=("Roboto", 12),
                bg="#FFFFFF", fg="#333333", justify=tk.LEFT).pack(pady=15)

        # 機器ID信息框
        id_frame = tk.Frame(info_frame, bg="#F5F5F5", relief=tk.SUNKEN, bd=1)
        id_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(id_frame, text="當前機器ID:", font=("Roboto", 10, "bold"),
                bg="#F5F5F5", fg="#333333").pack(anchor=tk.W, padx=10, pady=(10, 5))

        # 機器ID顯示和複製
        id_display_frame = tk.Frame(id_frame, bg="#F5F5F5")
        id_display_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        id_entry = tk.Entry(id_display_frame, font=("Consolas", 10), bg="#FFFFFF",
                           fg="#333333", relief=tk.FLAT, bd=1, state="readonly")
        id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        id_entry.config(state="normal")
        id_entry.insert(0, current_machine_id)
        id_entry.config(state="readonly")

        def copy_machine_id():
            dialog.clipboard_clear()
            dialog.clipboard_append(current_machine_id)
            copy_btn.config(text="已複製!", bg="#4CAF50")
            dialog.after(2000, lambda: copy_btn.config(text="複製", bg="#2196F3"))

        copy_btn = tk.Button(id_display_frame, text="複製", font=("Roboto", 9),
                           bg="#2196F3", fg="white", width=8, relief=tk.FLAT,
                           command=copy_machine_id)
        copy_btn.pack(side=tk.RIGHT)

        if authorized_id:
            tk.Label(id_frame, text=f"授權機器ID: {authorized_id}", font=("Roboto", 10),
                    bg="#F5F5F5", fg="#666666").pack(anchor=tk.W, padx=10, pady=(0, 10))

        # 說明文字
        instruction_text = ("請將上方的機器ID提供給管理員，\n"
                          "管理員需要使用授權工具生成對應的授權檔案。")
        tk.Label(info_frame, text=instruction_text, font=("Roboto", 10),
                bg="#FFFFFF", fg="#666666", justify=tk.CENTER).pack(pady=10)

        # 按鈕
        button_frame = tk.Frame(dialog, bg="#ECEFF1")
        button_frame.pack(pady=15)

        tk.Button(button_frame, text="確定", font=("Roboto", 12, "bold"),
                 bg="#F44336", fg="white", width=12, relief=tk.FLAT,
                 command=dialog.destroy).pack()

        # 等待對話框關閉
        dialog.wait_window()

    def check_authorization(self, show_gui=True, exit_on_fail=True):
        """
        檢查機器授權

        Args:
            show_gui (bool): 是否顯示GUI錯誤對話框，False則只打印錯誤
            exit_on_fail (bool): 授權失敗時是否退出程式

        Returns:
            bool: 授權是否通過
        """
        current_machine_id = self.get_machine_id()

        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    content = f.read().strip()

                # 檢查是否為有效的授權格式（應該包含簽名）
                if '|' not in content:
                    error_msg = "授權檔案格式錯誤"
                    if show_gui:
                        self.show_authorization_error(current_machine_id, "", error_msg)
                    else:
                        print(f"授權錯誤: {error_msg}")

                    if exit_on_fail:
                        sys.exit(1)
                    return False

                authorized_id, signature = content.split('|', 1)

                # 驗證機器ID
                if current_machine_id != authorized_id:
                    if show_gui:
                        self.show_authorization_error(current_machine_id, authorized_id, "機器ID不匹配")
                    else:
                        print(f"授權錯誤: 機器ID不匹配 - 當前: {current_machine_id}, 授權: {authorized_id}")

                    if exit_on_fail:
                        sys.exit(1)
                    return False

                # 驗證簽名
                expected_signature = self.generate_signature(authorized_id)
                if signature != expected_signature:
                    if show_gui:
                        self.show_authorization_error(current_machine_id, authorized_id, "授權檔案簽名無效")
                    else:
                        print(f"授權錯誤: 簽名無效")

                    if exit_on_fail:
                        sys.exit(1)
                    return False

                print(f"NVRV-Free 機器授權驗證成功，機器ID: {current_machine_id}")
                return True

            except Exception as e:
                error_msg = f"讀取授權檔案錯誤: {e}"
                if show_gui:
                    self.show_authorization_error(current_machine_id, "", error_msg)
                else:
                    print(f"授權錯誤: {error_msg}")

                if exit_on_fail:
                    sys.exit(1)
                return False
        else:
            # 沒有授權檔案
            if show_gui:
                self.show_authorization_error(current_machine_id, "", "未找到授權檔案")
            else:
                print(f"授權錯誤: 未找到授權檔案 - 機器ID: {current_machine_id}")

            if exit_on_fail:
                sys.exit(1)
            return False


# 設置主題和顏色
THEME_COLOR = "#3498db"  # 主題藍色
BACKGROUND_COLOR = "#f5f5f5"  # 淺灰色背景
ACCENT_COLOR = "#2ecc71"  # 強調綠色
WARNING_COLOR = "#e74c3c"  # 警告紅色
TEXT_COLOR = "#2c3e50"  # 深灰色文字

class CustomFileHandler(logging.FileHandler):
    def emit(self, record):
        """
        重寫 emit 方法，確保每次寫入後關閉檔案
        """
        try:
            if self.stream is None:
                self.stream = self._open()
            logging.FileHandler.emit(self, record)
            self.stream.close()
            self.stream = None
        except Exception:
            self.handleError(record)

# 設置日誌處理器
log_handler = CustomFileHandler(
    filename='rtsp_recorder50.log',  # 修改為 50 通道版本的日誌檔名
    encoding='utf-8',
    mode='a'
)
log_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
)

# 獲取根日誌記錄器並設置
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(log_handler)

class RTSPChannel:
    # 添加類變量來追踪當前打開的視窗
    current_calendar_window = None
    current_file_window = None
    current_video_window = None
    current_active_channel = None

    def __init__(self, parent, index, root):
        # 创建一个新的样式
        style = ttk.Style()
        style.configure(f"Channel.TLabelframe", background=BACKGROUND_COLOR)  # 使用淺灰色背景
        style.configure(f"Channel.TLabelframe.Label", background=BACKGROUND_COLOR, foreground=TEXT_COLOR)  # 使用深灰色文字

        self.index = index
        self.rtsp_url = tk.StringVar()
        self.channel_name = tk.StringVar(value=f"Channel {index+1}")
        self.record_duration = tk.IntVar(value=20)
        self.save_path = tk.StringVar(value="./recordings")
        self.keep_days = tk.IntVar(value=7)
        self.record_fps = tk.IntVar(value=8)
        self.use_source_fps = tk.BooleanVar(value=False)  # 添加使用原始FPS的變數
        self.rtsp_status = tk.StringVar(value="Normal")
        self.recording = False
        self.stop_event = threading.Event()
        self.cap = None
        self.out = None
        self.preview_img = None  # Reference to preview image to prevent garbage collection
        self.is_shutting_down = False  # 添加關閉標記
        self.preview_thread = None
        self.record_thread = None
        self.rtsp_reconnect_timeout = 3  # 將重連超時時間設置為3秒
        self.max_reconnect_attempts = 300  # 最大重連次數
        self.reconnect_count = 0  # 重連計數器
        self.frame_timeout = 5  # 讀取超時時間（秒）
        self.stream_timeout = 30  # RTSP串流超時時間（秒）
        self.last_frame_time = 0  # 上次成功讀取幀的時間
        self.preview_lock = threading.Lock()  # 添加程鎖
        self.frame_queue = []  # 用於存預覽幀
        self.max_queue_size = 3  # 最大存幀數
        self.frame_count = 0  # 添加幀數器
        self.preview_interval = 0.5  # 降低預覽更新頻率，減少卡頓
        self.root = root  # 保存主窗口的引用
        self.channel_id = index  # 添加頻道ID

        # 使用自定义样式
        self.frame = ttk.LabelFrame(parent, text=f"Channel {index+1}", style="Channel.TLabelframe")
        self.frame.grid(row=index // 5, column=index % 5, padx=5, pady=5, sticky="nsew")  # 改為5個頻道一行

        # 主要顯示區域
        main_frame = ttk.Frame(self.frame)
        main_frame.grid(row=0, column=0, columnspan=3, sticky="nsew", padx=5, pady=5)

        # 頻道名稱放在主要顯示區域
        ttk.Label(main_frame, text="Channel Name:", foreground=TEXT_COLOR).grid(row=0, column=0, sticky="w")
        name_entry = ttk.Entry(main_frame, textvariable=self.channel_name)
        name_entry.grid(row=0, column=1, columnspan=2, sticky="ew", padx=3, pady=3)
        name_entry.config(font=("Arial", 9))

        # 控制按鈕區域 - 使用更現代的按鈕設計
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, pady=5)

        # 定義按鈕樣式
        style.configure("Start.TButton", background=ACCENT_COLOR, foreground="white")
        style.configure("Stop.TButton", background=WARNING_COLOR, foreground="white")
        style.configure("Settings.TButton", background=THEME_COLOR, foreground="white")
        style.configure("Play.TButton", background=THEME_COLOR, foreground="white")

        self.start_button = ttk.Button(control_frame, text="Start", command=self.start_recording, width=5, style="Start.TButton")
        self.start_button.pack(side="left", padx=2)

        self.stop_button = ttk.Button(control_frame, text="Stop", command=self.stop_recording, state="disabled", width=5, style="Stop.TButton")
        self.stop_button.pack(side="left", padx=2)

        # Settings 按鈕移到控制按鈕區域
        settings_toggle = ttk.Button(control_frame, text="⚙", command=self.toggle_settings, width=3, style="Settings.TButton")
        settings_toggle.pack(side="left", padx=2)

        # 新增 "Play" 按鈕
        play_button = ttk.Button(control_frame, text="Play", command=self.play_recording, width=5, style="Play.TButton")
        play_button.pack(side="left", padx=2)

        # 預覽和狀態區域
        preview_frame = ttk.Frame(main_frame, relief=tk.RIDGE, borderwidth=1)
        preview_frame.grid(row=2, column=0, columnspan=3, pady=5)

        self.preview_canvas = tk.Canvas(preview_frame, width=160, height=120, bg="black", highlightthickness=0)
        self.preview_canvas.pack(side="left", padx=5, pady=5)
        self.preview_canvas.create_text(80, 60, text="Ready", fill="white", font=("Arial", 10, "bold"))

        self.status_label = tk.Label(preview_frame, width=10, height=2, bg="#FFD700", fg="black", text="就緒", font=("Arial", 9, "bold"))
        self.status_label.pack(side="left", padx=5)

        # 創建設定區域框架 - 使用更現代的設計
        self.settings_frame = ttk.Frame(self.frame, relief=tk.RIDGE, borderwidth=1)
        self.settings_frame.grid(row=1, column=0, columnspan=3, sticky="nsew", padx=5, pady=5)
        self.settings_frame.grid_remove()  # 初始時隱藏

        # 設定區域的內容
        # RTSP URL 移到設定區域
        ttk.Label(self.settings_frame, text="RTSP URL:", foreground=TEXT_COLOR).grid(row=0, column=0, sticky="w", padx=3, pady=3)
        url_entry = ttk.Entry(self.settings_frame, textvariable=self.rtsp_url, width=30)
        url_entry.grid(row=0, column=1, columnspan=2, sticky="ew", padx=3, pady=3)
        url_entry.config(font=("Arial", 9))

        ttk.Label(self.settings_frame, text="Record Duration (min):", foreground=TEXT_COLOR).grid(row=1, column=0, sticky="w", padx=3, pady=3)
        duration_spinbox = ttk.Spinbox(self.settings_frame, from_=1, to=120, textvariable=self.record_duration)
        duration_spinbox.grid(row=1, column=1, sticky="ew", padx=3, pady=3)
        duration_spinbox.config(font=("Arial", 9))

        ttk.Label(self.settings_frame, text="FPS:", foreground=TEXT_COLOR).grid(row=2, column=0, sticky="w", padx=3, pady=3)
        fps_frame = ttk.Frame(self.settings_frame)
        fps_frame.grid(row=2, column=1, sticky="ew", padx=3, pady=3)
        
        # 新增使用原始FPS的選項
        use_source_checkbox = ttk.Checkbutton(fps_frame, text="使用原始串流FPS", variable=self.use_source_fps)
        use_source_checkbox.pack(side="top", anchor="w")
        
        # FPS 輸入框
        fps_spinbox = ttk.Spinbox(fps_frame, from_=1, to=120, textvariable=self.record_fps)
        fps_spinbox.pack(side="top", fill="x", expand=True)
        fps_spinbox.config(font=("Arial", 9))
        
        # 當勾選使用原始FPS時，禁用FPS輸入框
        def toggle_fps_input():
            if self.use_source_fps.get():
                fps_spinbox.config(state="disabled")
            else:
                fps_spinbox.config(state="normal")
        
        # 綁定勾選事件
        use_source_checkbox.config(command=toggle_fps_input)
        
        # 初始化狀態
        toggle_fps_input()

        ttk.Label(self.settings_frame, text="Save Path:", foreground=TEXT_COLOR).grid(row=3, column=0, sticky="w", padx=3, pady=3)
        path_frame = ttk.Frame(self.settings_frame)
        path_frame.grid(row=3, column=1, sticky="ew", padx=3, pady=3)
        path_entry = ttk.Entry(path_frame, textvariable=self.save_path)
        path_entry.pack(side="left", fill="x", expand=True)
        path_entry.config(font=("Arial", 9))
        browse_button = ttk.Button(path_frame, text="...", command=self.select_save_path, width=3)
        browse_button.pack(side="right")

        ttk.Label(self.settings_frame, text="Keep Days:", foreground=TEXT_COLOR).grid(row=4, column=0, sticky="w", padx=3, pady=3)
        days_spinbox = ttk.Spinbox(self.settings_frame, from_=1, to=365, textvariable=self.keep_days)
        days_spinbox.grid(row=4, column=1, sticky="ew", padx=3, pady=3)
        days_spinbox.config(font=("Arial", 9))

        # 在設定區域添加儲存按鈕
        save_button = ttk.Button(self.settings_frame, text="Save Settings", command=self.save_channel_settings, style="Settings.TButton")
        save_button.grid(row=5, column=0, columnspan=2, pady=5)  # 放在最後一行

    def toggle_settings(self):
        """切換設定區域的顯示狀態，並在關閉時自動保存更改的設定"""
        if self.settings_frame.winfo_viewable():
            # 檢查當前設定是否與定檔一致
            if self.is_settings_changed():
                self.save_channel_settings()
            self.settings_frame.grid_remove()
        else:
            self.settings_frame.grid()

    def is_settings_changed(self):
        """檢查當前設定是否與設定檔一致"""
        try:
            if os.path.exists("settings_free.json"):  # 修改設定檔名稱
                # 添加 utf-8 編碼
                with open("settings_free.json", "r", encoding='utf-8') as f:  # 修改設定檔名稱
                    data = json.load(f)
                    if self.index < len(data):
                        channel_settings = data[self.index]
                        return (
                            channel_settings.get("rtsp_url", "") != self.rtsp_url.get() or
                            channel_settings.get("channel_name", "") != self.channel_name.get() or
                            channel_settings.get("record_duration", 60) != self.record_duration.get() or
                            channel_settings.get("save_path", "./recordings") != self.save_path.get() or
                            channel_settings.get("keep_days", 100) != self.keep_days.get() or
                            channel_settings.get("record_fps", 8) != self.record_fps.get() or
                            channel_settings.get("use_source_fps", False) != self.use_source_fps.get()
                        )
        except Exception as e:
            logging.error(f"Error checking settings: {str(e)}")
        return False

    def select_save_path(self):
        path = filedialog.askdirectory()
        if path:
            self.save_path.set(path)

    def start_recording(self):
        if not self.rtsp_url.get():
            messagebox.showerror("Error", "RTSP URL cannot be empty!")
            return
            
        try:
            # 在開始錄影前清理舊檔案
            self.clean_old_directories()
            
            self.recording = True
            self.stop_event.clear()
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

            # 使用實例變量保存線程引用
            self.record_thread = threading.Thread(target=self.record_stream, daemon=True)
            self.preview_thread = threading.Thread(target=self.update_preview, daemon=True)
            
            self.record_thread.start()
            self.preview_thread.start()
            
        except Exception as e:
            logging.error(f"Error starting recording: {e}")
            self.recording = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def stop_recording(self):
        try:
            logging.info(f"Stopping recording for channel {self.channel_name.get()}")
            
            # 先停止預覽線程
            self.stop_event.set()
            if self.preview_thread and self.preview_thread.is_alive():
                self.preview_thread.join(timeout=1)
            
            # 確保最後一個文件被正保存
            if self.out is not None:
                try:
                    self.out.release()
                    self.out = None
                    
                    # 等待文件寫入完成
                    time.sleep(0.5)
                    
                    # 檢查並重命名最後的時文件
                    current_date = datetime.now().strftime("%Y%m%d")
                    date_dir = os.path.join(self.save_path.get(), current_date)
                    
                    # 查找最新的臨時文件
                    temp_files = [f for f in os.listdir(date_dir) if f.startswith(f"temp_{self.channel_name.get()}")]
                    if temp_files:
                        temp_file = os.path.join(date_dir, max(temp_files))  # 獲取最新的臨時件
                        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 2 * 1024:
                            final_file = temp_file.replace("temp_", "")
                            try:
                                os.rename(temp_file, final_file)
                                logging.info(f"Successfully saved final recording to {final_file}")
                            except Exception as e:
                                logging.error(f"Error renaming final file: {e}")
                        else:
                            try:
                                os.remove(temp_file)
                            except:
                                pass
                except Exception as e:
                    logging.error(f"Error handling final file: {e}")
            
            # 停止錄影
            self.recording = False
            
            # 釋放像頭資源
            if self.cap is not None:
                self.cap.release()
                self.cap = None
            
            # 等待錄影線程結束
            if self.record_thread and self.record_thread.is_alive():
                self.record_thread.join(timeout=2)
            
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
            # 在停止錄影後清理舊檔案
            self.clean_old_directories()
            
        except Exception as e:
            logging.error(f"Error in stop_recording: {e}")
        finally:
            # 確保所有資源都被釋放
            try:
                if self.cap is not None:
                    self.cap.release()
                    self.cap = None
                if self.out is not None:
                    self.out.release()
                    self.out = None
            except:
                pass

    def clean_old_directories(self):
        try:
            # 先檢查設定檔中的保留天數
            if os.path.exists("settings_free.json"):  # 修改設定檔名稱
                try:
                    # 使用 utf-8 編碼讀取文件
                    with open("settings_free.json", "r", encoding='utf-8') as f:  # 修改設定檔名稱
                        settings = json.load(f)
                        if self.index < len(settings):
                            channel_settings = settings[self.index]
                            keep_days = channel_settings.get("keep_days", self.keep_days.get())
                            self.keep_days.set(keep_days)  # 更新當前設定
                except Exception as e:
                    logging.error(f"Error reading keep_days from settings: {str(e)}")
                    keep_days = self.keep_days.get()  # 使用當前設定
            else:
                keep_days = self.keep_days.get()  # 使用當前設定

            base_dir = self.save_path.get()
            if not os.path.exists(base_dir):
                return

            cutoff_date = datetime.now() - timedelta(days=keep_days)

            for folder in os.listdir(base_dir):
                folder_path = os.path.join(base_dir, folder)
                if os.path.isdir(folder_path):
                    try:
                        folder_date = datetime.strptime(folder, "%Y%m%d")
                        if folder_date < cutoff_date:
                            for root, dirs, files in os.walk(folder_path, topdown=False):
                                for name in files:
                                    os.remove(os.path.join(root, name))
                                for name in dirs:
                                    os.rmdir(os.path.join(root, name))
                            os.rmdir(folder_path)
                            logging.info(f"Cleaned old directory: {folder_path}")
                    except ValueError:
                        logging.warning(f"Skipping invalid date format folder: {folder}")
                    except Exception as e:
                        logging.error(f"Error cleaning folder {folder_path}: {e}")
        except Exception as e:
            logging.error(f"Error in clean_old_directories: {e}")

    def save_current_recording(self):
        """保存當前錄影檔案"""
        if self.out is not None:
            try:
                self.out.release()
                self.out = None
                
                # 等待文件寫入完成
                time.sleep(0.5)
                
                # 檢查並重命名最後的臨時文件
                current_date = datetime.now().strftime("%Y%m%d")
                date_dir = os.path.join(self.save_path.get(), current_date)
                
                # 查找最新的臨時文件
                temp_files = [f for f in os.listdir(date_dir) if f.startswith(f"temp_{self.channel_name.get()}")]
                if temp_files:
                    temp_file = os.path.join(date_dir, max(temp_files))
                    if os.path.exists(temp_file) and os.path.getsize(temp_file) > 2 * 1024:
                        final_file = temp_file.replace("temp_", "")
                        try:
                            os.rename(temp_file, final_file)
                            logging.info(f"Successfully saved recording to {final_file}")
                        except Exception as e:
                            logging.error(f"Error renaming file: {e}")
                    else:
                        try:
                            os.remove(temp_file)
                        except:
                            pass
            except Exception as e:
                logging.error(f"Error saving current recording: {e}")

    def record_stream(self):
        logging.info(f"Starting recording thread for channel {self.channel_name.get()}")
        last_check_date = datetime.now().date()
        
        while self.recording:
            try:
                # 檢查日期是否改變
                current_date = datetime.now().date()
                if current_date != last_check_date:
                    logging.info(f"Date changed, cleaning old directories and logs for channel {self.channel_name.get()}")
                    self.clean_old_directories()
                    last_check_date = current_date
                
                if self.cap is None or not self.cap.isOpened():
                    # 先保存當前錄
                    self.save_current_recording()
                    
                    # 嘗試重新連接
                    logging.info(f"Attempting to connect to RTSP stream: {self.rtsp_url.get()}")
                    self.cap = cv2.VideoCapture(self.rtsp_url.get())
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    if not self.cap.isOpened():
                        logging.warning(f"Failed to connect to RTSP stream: {self.rtsp_url.get()}")
                        time.sleep(self.rtsp_reconnect_timeout)
                        continue

                    logging.info(f"Successfully connected to RTSP stream: {self.rtsp_url.get()}")
                    
                    # 重新初始化影
                    self.out = None  # 確保新的錄影檔案會創建

                current_date = datetime.now().strftime("%Y%m%d")
                date_dir = os.path.join(self.save_path.get(), current_date)
                os.makedirs(date_dir, exist_ok=True)

                # 在創建新檔案前檢查舊檔案
                self.clean_old_directories()

                temp_file = os.path.join(
                    date_dir,
                    f"temp_{self.channel_name.get()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
                )
                
                start_time = datetime.now()

                # 改用 avc1 編碼器
                fourcc = cv2.VideoWriter_fourcc(*'avc1')
                
                # 獲取第設尺
                ret, frame = self.cap.read()
                if not ret:
                    logging.warning("Failed to read frame, attempting to reconnect...")
                    self.cap.release()
                    time.sleep(self.rtsp_reconnect_timeout)
                    continue
                    
                # 持原始解析度
                width = frame.shape[1]
                height = frame.shape[0]
                
                # 決定使用的FPS
                recording_fps = self.record_fps.get()
                if self.use_source_fps.get():
                    # 獲取原始串流的FPS
                    source_fps = self.cap.get(cv2.CAP_PROP_FPS)
                    if source_fps > 0:
                        recording_fps = source_fps
                        logging.info(f"Using source FPS: {recording_fps} for channel {self.channel_name.get()}")
                
                # 初始化 VideoWriter，設置壓縮參數
                self.out = cv2.VideoWriter(temp_file, 
                                         fourcc,
                                         recording_fps,
                                         (width, height),
                                         isColor=True)

                while self.recording and (datetime.now() - start_time).seconds < self.record_duration.get() * 60:
                    if not self.cap.isOpened():
                        break
                        
                    ret, frame = self.cap.read()
                    if not ret:
                        logging.warning("Failed to read frame, attempting to reconnect...")
                        self.cap.release()
                        time.sleep(self.rtsp_reconnect_timeout)
                        break

                    self.frame_count += 1
                    self.last_frame_time = time.time()

                    try:
                        # 添加時間戳
                        current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        cv2.putText(frame, current_timestamp, 
                                  (10, frame.shape[0] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.75, (255, 255, 255), 2, 
                                  cv2.LINE_AA)

                        # 寫入彩色幀
                        self.out.write(frame)
                        
                        # 更新預覽幀
                        if self.frame_count % max(1, int(recording_fps * self.preview_interval)) == 0:
                            preview_frame = cv2.resize(frame, (160, 120))
                            with self.preview_lock:
                                self.frame_queue = [preview_frame]
                    
                    except Exception as e:
                        logging.error(f"Error processing frame: {e}")
                        break

                # 處理檔案
                if self.out is not None:
                    try:
                        self.out.release()
                        self.out = None
                        time.sleep(0.5)  # 等待完成
                        
                        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 1 * 1024 * 1024:
                            final_file = temp_file.replace("temp_", "")
                            try:
                                os.rename(temp_file, final_file)
                                logging.info(f"Successfully saved recording to {final_file}")
                            except Exception as e:
                                logging.error(f"Error renaming file: {e}")
                        else:
                            try:
                                os.remove(temp_file)
                            except:
                                pass
                    except Exception as e:
                        logging.error(f"Error handling final file: {e}")

            except Exception as e:
                logging.error(f"Recording error: {e}")
                time.sleep(1)
            finally:
                if self.cap is not None:
                    self.cap.release()
                    self.cap = None

    def update_preview(self):
        preview_interval = 0.2
        last_update = 0
        
        while self.recording:
            try:
                current_time = time.time()
                if current_time - last_update < preview_interval:
                    time.sleep(0.01)
                    continue

                # 從緩存中獲取預覽幀
                preview_frame = None
                with self.preview_lock:
                    if self.frame_queue:
                        preview_frame = self.frame_queue[-1].copy()
                        self.frame_queue.clear()

                # 檢查是否超過超時時間
                if current_time - self.last_frame_time > self.frame_timeout:
                    self.preview_canvas.delete("all")
                    self.preview_canvas.create_text(80, 60, text="No Signal", fill="white", font=("Arial", 12, "bold"))
                    self.status_label.config(bg=WARNING_COLOR, fg="white", text="離線")  # 斷線時為紅色
                    time.sleep(0.1)
                    continue

                if preview_frame is None:
                    time.sleep(0.1)
                    continue

                try:
                    # 降低預覽圖像品質
                    rgb_frame = cv2.cvtColor(preview_frame, cv2.COLOR_BGR2RGB)
                    
                    # 使用 PIL 進行圖像轉換
                    pil_image = Image.fromarray(rgb_frame)
                    img = ImageTk.PhotoImage(pil_image)
                    
                    self.preview_canvas.delete("all")
                    self.preview_canvas.create_image(80, 60, image=img, anchor="center")
                    self.preview_img = img
                    self.status_label.config(bg=ACCENT_COLOR, fg="white", text="錄影中")  # 正常接收時為綠色
                    
                    last_update = current_time
                    
                except Exception as e:
                    logging.error(f"Error processing preview frame: {e}")
                    time.sleep(0.1)

            except Exception as e:
                logging.error(f"Preview error: {e}")
                time.sleep(0.5)

        # 停止錄影時
        try:
            self.preview_canvas.delete("all")
            self.preview_canvas.create_text(80, 60, text="Ready", fill="white", font=("Arial", 12, "bold"))
            self.status_label.config(bg="#FFD700", text="就緒")  # 停止時為黃色
            self.preview_img = None
            with self.preview_lock:
                self.frame_queue.clear()
        except:
            pass

    def save_channel_settings(self):
        """儲存當前頻道的設定"""
        try:
            # 讀取現有的設定
            data = []
            if os.path.exists("settings_free.json"):
                with open("settings_free.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                
            # 確保data列表足夠長
            while len(data) <= self.index:
                data.append({})
            
            # 更新當前頻道的設定
            new_settings = {
                "rtsp_url": self.rtsp_url.get(),
                "channel_name": self.channel_name.get(),
                "record_duration": self.record_duration.get(),
                "save_path": self.save_path.get(),
                "keep_days": self.keep_days.get(),
                "record_fps": self.record_fps.get(),
                "use_source_fps": self.use_source_fps.get(),
            }
            
            # 比較新舊設定，並記錄變動
            old_settings = data[self.index]
            for key, new_value in new_settings.items():
                old_value = old_settings.get(key)
                if old_value != new_value:
                    logging.info(f"Channel {self.index + 1} setting changed: {key} from {old_value} to {new_value}")

            data[self.index] = new_settings

            # 寫入設定檔
            with open("settings_free.json", "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)  # 確保非ASCII字符

        except Exception as e:
            logging.error(f"Error saving channel settings: {e}")
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def play_recording(self):
        """播放錄影"""
        # 檢查是否有播放視窗正在錄影
        if RTSPChannel.current_video_window:
            # 獲取視窗的所有子部件
            for widget in RTSPChannel.current_video_window.winfo_children():
                if isinstance(widget, ttk.Frame):  # 尋找控制框架
                    for btn in widget.winfo_children():
                        if isinstance(btn, ttk.Button) and btn.cget('text') == "停止錄影":
                            # 如果找到"停止錄影"按鈕，表示正在錄影
                            btn.invoke()  # 觸發按鈕的點擊事件，會自動保存錄影
                            break

        # 關閉所有已開啟的視窗
        # 獲取所有 Toplevel 視窗
        for window in self.root.winfo_toplevel().winfo_children():
            if isinstance(window, tk.Toplevel):
                window.destroy()

        # 關閉當前追踪的視窗
        if RTSPChannel.current_file_window:
            RTSPChannel.current_file_window.destroy()
            RTSPChannel.current_file_window = None
        if RTSPChannel.current_video_window:
            RTSPChannel.current_video_window.destroy()
            RTSPChannel.current_video_window = None
        if RTSPChannel.current_calendar_window:
            RTSPChannel.current_calendar_window.destroy()
            RTSPChannel.current_calendar_window = None

        # 檢查是否有其他頻道的視窗正在運行
        if (RTSPChannel.current_active_channel is not None and 
            RTSPChannel.current_active_channel != self.channel_id):
            RTSPChannel.current_active_channel = None

        # 設置當前活動頻道
        RTSPChannel.current_active_channel = self.channel_id

        # 創建新的日曆視窗
        calendar_window = tk.Toplevel(self.root)
        RTSPChannel.current_calendar_window = calendar_window
        calendar_window.title("Select Date to Play")
        
        # 創建日曆小件
        cal = Calendar(calendar_window, selectmode='day')
        cal.pack(pady=20)

        # 獲取存檔目錄
        save_path = self.save_path.get()

        # 獲取當前日期
        today = datetime.now().date()

        # 設置日曆的日期範圍
        start_date = today - timedelta(days=365)  # 假設最多顯示過去一年的日期
        end_date = today

        # 檢查每一天是否有存檔
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime("%Y%m%d")
            date_dir = os.path.join(save_path, date_str)
            if os.path.exists(date_dir) and os.listdir(date_dir):
                # 如果目錄存在且不為空，設置高亮顏色
                cal.calevent_create(current_date, 'Recording Available', 'highlight')
            else:
                # 如果目錄不存在或為空，設置暗色顏色
                cal.calevent_create(current_date, 'No Recording', 'faint')
            current_date += timedelta(days=1)

        # 設置高亮和暗色顏色
        cal.tag_config('highlight', background='green', foreground='white')
        cal.tag_config('faint', background='gray', foreground='black')

        # 修改確認選擇的函數
        def on_date_select():
            try:
                # 獲取選擇的日期並轉換為正確的格式
                selected_date_str = cal.get_date()
                try:
                    selected_date = datetime.strptime(selected_date_str, "%Y/%m/%d")
                except ValueError:
                    try:
                        selected_date = datetime.strptime(selected_date_str, "%m/%d/%y")
                    except ValueError:
                        selected_date = datetime.strptime(selected_date_str, "%m/%d/%Y")

                date_str = selected_date.strftime("%Y%m%d")
                date_dir = os.path.join(save_path, date_str)

                # 先關閉日曆窗口
                calendar_window.destroy()

                if os.path.exists(date_dir):
                    # 創建文件列表視窗
                    file_window = tk.Toplevel(self.root)
                    RTSPChannel.current_file_window = file_window
                    file_window.title(f"錄影檔案列表 - {date_str}")
                    file_window.geometry("500x400")
                    file_window.attributes('-topmost', True)

                    # 創建主框架
                    main_frame = ttk.Frame(file_window)
                    main_frame.pack(fill='both', expand=True, padx=10)

                    # 創建列表框架（帶滾動條）
                    list_frame = ttk.Frame(main_frame)
                    list_frame.pack(fill='both', expand=True)

                    # 創建滾動條
                    scrollbar = ttk.Scrollbar(list_frame)
                    scrollbar.pack(side='right', fill='y')

                    # 創建列表框來顯示文件
                    file_listbox = tk.Listbox(
                        list_frame, 
                        width=60, 
                        height=20,
                        yscrollcommand=scrollbar.set,
                        font=('Arial', 10)
                    )
                    file_listbox.pack(side='left', fill='both', expand=True)
                    scrollbar.config(command=file_listbox.yview)

                    # 列出文件並添加到列表中
                    mp4_files = sorted([f for f in os.listdir(date_dir) if f.endswith('.mp4')])
                    for file_name in mp4_files:
                        try:
                            file_path = os.path.join(date_dir, file_name)
                            # 獲取視頻時長
                            cap = cv2.VideoCapture(file_path)
                            if cap.isOpened():
                                fps = cap.get(cv2.CAP_PROP_FPS)
                                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                                duration = frame_count / fps if fps > 0 else 0
                                cap.release()
                                duration_str = str(timedelta(seconds=int(duration)))
                                file_listbox.insert(tk.END, f"{file_name} - {duration_str}")
                        except Exception as e:
                            logging.error(f"Error processing file {file_name}: {e}")
                            continue

                    # 創建按鈕框架
                    button_frame = ttk.Frame(file_window)
                    button_frame.pack(side='bottom', fill='x', padx=10, pady=10)

                    # 設置按鈕樣式
                    style = ttk.Style()
                    style.configure('Play.TButton', 
                                  padding=(10, 8),
                                  font=('Arial', 10))

                    # 在創建按鈕之前，先定義 reselect_date 函數
                    def reselect_date():
                        file_window.destroy()
                        self.play_recording()

                    # 定義播放函數
                    def play_selected_file():
                        if file_listbox.curselection():
                            selected_file = file_listbox.get(file_listbox.curselection())
                            file_name = selected_file.split(' - ')[0]
                            file_path = os.path.join(date_dir, file_name)
                            file_window.withdraw()  # 隱藏文件列表窗口
                            self.play_video(file_path, file_window)  # 傳遞文件列表窗口引用

                    # 創建播放按鈕
                    play_button = ttk.Button(
                        button_frame, 
                        text="播放選擇的檔案", 
                        command=play_selected_file,
                        style='Play.TButton',
                        width=20
                    )
                    play_button.pack(side='left', padx=5, ipady=5)

                    # 再創建"選擇日期"按鈕
                    select_date_button = ttk.Button(
                        button_frame,
                        text="選擇日期",
                        command=reselect_date,
                        style='Play.TButton',
                        width=20
                    )
                    select_date_button.pack(side='left', padx=5, ipady=5)

            except Exception as e:
                logging.error(f"Error in on_date_select: {e}")
                messagebox.showerror("Error", f"無法讀取檔案列表: {str(e)}")

        select_button = ttk.Button(calendar_window, text="Select", command=on_date_select)
        select_button.pack(pady=10)

    def play_video(self, file_path, file_window=None):
        """播放視頻文件"""
        # 創建視頻視窗
        video_window = tk.Toplevel(self.root)
        RTSPChannel.current_video_window = video_window
        
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            if file_window:
                file_window.deiconify()  # 如果打不開視頻，顯示文件列表
            messagebox.showerror("Error", "Cannot open video file!")
            return

        # 獲取視頻總數和 FPS
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0

        # 獲取視頻的原始尺寸
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 創建畫布來顯示視頻幀
        canvas = tk.Canvas(video_window, width=width, height=height)
        canvas.pack(expand=True, fill='both')

        # 創建控框架
        control_frame = ttk.Frame(video_window)
        control_frame.pack(fill='x', padx=5, pady=5)

        # 創建時間標籤
        time_label = ttk.Label(control_frame, text="00:00:00 / " + str(timedelta(seconds=int(duration))))
        time_label.pack(side='left', padx=5)

        # 創建進度條
        progress_var = tk.DoubleVar()
        progress = ttk.Scale(
            control_frame,
            from_=0,
            to=total_frames-1,  # 修正最大值
            orient='horizontal',
            variable=progress_var,
            length=300
        )
        progress.pack(side='left', fill='x', expand=True, padx=5)

        # 添加暫停/播放按鈕
        is_playing = True
        def toggle_play():
            nonlocal is_playing
            is_playing = not is_playing
            play_button.config(text="暫停" if is_playing else "播放")

        play_button = ttk.Button(control_frame, text="暫停", command=toggle_play)
        play_button.pack(side='left', padx=5)

        # 添加關閉按鈕
        def close_video():
            if is_recording:
                if messagebox.askyesno("確認", "正在錄影中，確定要關閉嗎？"):
                    if recording_writer:
                        recording_writer.release()
                    cap.release()
                    video_window.destroy()
                    RTSPChannel.current_video_window = None
                    if file_window:
                        file_window.deiconify()
            else:
                cap.release()
                video_window.destroy()
                RTSPChannel.current_video_window = None
                if file_window:
                    file_window.deiconify()

        close_button = ttk.Button(control_frame, text="關閉", command=close_video)
        close_button.pack(side='right', padx=5)

        # 進度條拖動事件
        def on_progress_change(event=None):  # 添加默認參數
            try:
                frame_pos = int(progress_var.get())
                # 確保幀位置在有效範圍內
                frame_pos = max(0, min(frame_pos, total_frames-1))
                
                # 設置新的幀位置
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                
                # 更新時間標籤
                current_time = frame_pos / fps if fps > 0 else 0
                time_label.config(text=f"{str(timedelta(seconds=int(current_time)))} / {str(timedelta(seconds=int(duration)))}")
                
                # 立即更新一幀
                ret, frame = cap.read()
                if ret:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame)
                    imgtk = ImageTk.PhotoImage(image=img)
                    video_window.images.clear()
                    video_window.images.append(imgtk)
                    canvas.delete("all")
                    canvas.create_image(width//2, height//2, image=imgtk, anchor='center')
            except Exception as e:
                logging.error(f"Error in progress change: {e}")

        # 綁定多個進度條事件
        progress.bind("<ButtonRelease-1>", on_progress_change)  # 鼠標釋放時
        progress.bind("<B1-Motion>", on_progress_change)  # 鼠標拖動
        progress.bind("<Button-1>", lambda e: toggle_play() if is_playing else None)  # 點擊時如果正在播放就暫停

        # 保存對圖像的引用
        video_window.images = []

        # 添加錄影相關變量
        is_recording = False
        recording_writer = None
        
        # 添加錄影按鈕
        def toggle_recording():
            nonlocal is_recording, recording_writer
            
            if not is_recording:
                # 開始錄影
                try:
                    # 詢問保存位置和文件名
                    save_path = filedialog.asksaveasfilename(
                        defaultextension=".mp4",
                        filetypes=[("MP4 files", "*.mp4")],
                        title="選擇保存位置和檔名"
                    )
                    
                    if save_path:  # 如果用戶沒有取消選擇
                        # 獲原始視頻的寬高
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        
                        # 創建視頻入器
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                        recording_writer = cv2.VideoWriter(
                            save_path, 
                            fourcc, 
                            fps, 
                            (width, height))  # 修正號的關閉位置
                        
                        is_recording = True
                        record_button.config(text="停止錄影")
                        
                        # 將視窗設置為最上層
                        video_window.lift()  # 提升視窗到最上層
                        video_window.focus_force()  # 強制獲取焦點
                        
                except Exception as e:
                    messagebox.showerror("Error", f"無法開始錄影: {str(e)}")

            else:
                # 停止錄影
                try:
                    if recording_writer:
                        recording_writer.release()
                        recording_writer = None
                    is_recording = False
                    record_button.config(text="開始錄影")
                    messagebox.showinfo("完成", "錄影已保存")
                    # 將視窗設置為最上層
                    video_window.lift()  # 提升視窗到最上層
                    video_window.focus_force()  # 強制獲取焦點
                except Exception as e:
                    messagebox.showerror("Error", f"無法停止錄影: {str(e)}")

        # 創建錄影按鈕
        record_button = ttk.Button(
            control_frame, 
            text="開始錄影",
            command=toggle_recording
        )
        record_button.pack(side='left', padx=5)

        # 添加播放速度變量
        playback_speed = tk.DoubleVar(value=1.0)

        # 創建速度控制按鈕框架
        speed_frame = ttk.Frame(control_frame)
        speed_frame.pack(side='left', padx=5)

        # 創建速度控制按鈕
        def change_speed(speed):
            playback_speed.set(speed)
            # 更新所有速度按鈕的狀態
            normal_speed_btn.state(['!pressed'] if speed != 1.0 else ['pressed'])
            double_speed_btn.state(['!pressed'] if speed != 2.0 else ['pressed'])
            quadruple_speed_btn.state(['!pressed'] if speed != 4.0 else ['pressed'])

        # 正常速按鈕
        normal_speed_btn = ttk.Button(
            speed_frame,
            text="1x",
            command=lambda: change_speed(1.0),
            style='Speed.TButton',
            width=3
        )
        normal_speed_btn.pack(side='left', padx=2)
        normal_speed_btn.state(['pressed'])  # 默認選中

        # 2倍速按鈕
        double_speed_btn = ttk.Button(
            speed_frame,
            text="2x",
            command=lambda: change_speed(2.0),
            style='Speed.TButton',
            width=3
        )
        double_speed_btn.pack(side='left', padx=2)

        # 4倍速按鈕
        quadruple_speed_btn = ttk.Button(
            speed_frame,
            text="4x",
            command=lambda: change_speed(4.0),
            style='Speed.TButton',
            width=3
        )
        quadruple_speed_btn.pack(side='left', padx=2)

        # 修改 update_frame 函數，將其放在上述代碼之後
        def update_frame():
            if is_playing:
                current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                # 根據播放速度跳過相應數量的幀
                skip_frames = int(playback_speed.get()) - 1
                if skip_frames > 0:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame + skip_frames)
                
                ret, frame = cap.read()
                if ret:
                    # 更新進度條和時間標籤
                    current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                    progress_var.set(current_frame)
                    current_time = current_frame / fps if fps > 0 else 0
                    time_label.config(text=f"{str(timedelta(seconds=int(current_time)))} / {str(timedelta(seconds=int(duration)))}")

                    # 如果正在錄影，寫入幀
                    if is_recording and recording_writer:
                        recording_writer.write(frame)

                    # 將幀轉換為圖像顯示
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame_rgb)
                    imgtk = ImageTk.PhotoImage(image=img)
                    
                    video_window.images.clear()
                    video_window.images.append(imgtk)
                    
                    canvas.delete("all")
                    canvas.create_image(width//2, height//2, image=imgtk, anchor='center')
                    
                    # 根據播放速度調整更新間隔
                    update_interval = int(30 / playback_speed.get())
                    video_window.after(update_interval, update_frame)
                else:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    update_frame()
            else:
                video_window.after(100, update_frame)

        # 修改窗口關閉理
        def on_closing():
            nonlocal recording_writer
            if is_recording:
                if messagebox.askyesno("確認", "正在錄影中，確定要關閉嗎？"):
                    if recording_writer:
                        recording_writer.release()
                    cap.release()
                    video_window.destroy()
                    RTSPChannel.current_video_window = None
                    if file_window:
                        file_window.deiconify()
            else:
                cap.release()
                video_window.destroy()
                RTSPChannel.current_video_window = None
                if file_window:
                    file_window.deiconify()

        video_window.protocol("WM_DELETE_WINDOW", on_closing)

        # 在控制框架中添加時間跳轉功能
        # 創建時間輸入框架
        time_jump_frame = ttk.Frame(control_frame)
        time_jump_frame.pack(side='left', padx=10)

        # 添標籤
        ttk.Label(time_jump_frame, text="跳轉到:").pack(side='left', padx=2)
        
        # 分鐘輸入框
        minute_var = tk.StringVar()
        minute_entry = ttk.Entry(time_jump_frame, textvariable=minute_var, width=3)
        minute_entry.pack(side='left')
        ttk.Label(time_jump_frame, text="分").pack(side='left')
        
        # 秒數輸入框
        second_var = tk.StringVar()
        second_entry = ttk.Entry(time_jump_frame, textvariable=second_var, width=3)
        second_entry.pack(side='left')
        ttk.Label(time_jump_frame, text="秒").pack(side='left')

        # 跳轉按鈕功能
        def jump_to_time():
            try:
                minutes = int(minute_var.get() or '0')
                seconds = int(second_var.get() or '0')
                total_seconds = minutes * 60 + seconds
                
                # 確保時間在有效範圍內
                if total_seconds > duration:
                    messagebox.showwarning("警告", "輸入時間超過影片長度！")
                    return
                
                # 計算幀位置
                frame_pos = int(total_seconds * fps)
                
                # 設置新的幀位置
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                
                # 更新進度條
                progress_var.set(frame_pos)
                
                # 更新時間標籤
                time_label.config(text=f"{str(timedelta(seconds=total_seconds))} / {str(timedelta(seconds=int(duration)))}")
                
                # 如果當前是暫停狀態，更新一幀
                if not is_playing:
                    ret, frame = cap.read()
                    if ret:
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        img = Image.fromarray(frame)
                        imgtk = ImageTk.PhotoImage(image=img)
                        video_window.images.clear()
                        video_window.images.append(imgtk)
                        canvas.delete("all")
                        canvas.create_image(width//2, height//2, image=imgtk, anchor='center')
                    
            except ValueError:
                messagebox.showerror("錯誤", "請輸入有效的數字！")

        # 添加跳轉按鈕
        jump_button = ttk.Button(time_jump_frame, text="跳轉", command=jump_to_time)
        jump_button.pack(side='left', padx=5)

        # 在控制框架中添加前一幀和後一幀按鈕
        # 放在播放/暫停按鈕之後

        # 前一幀按鈕功能
        def previous_frame():
            current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            if current_frame > 1:
                # 設置為前一幀
                cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame - 2)  # 減2是因為讀取會前進1幀
                ret, frame = cap.read()
                if ret:
                    # 更新進度條
                    progress_var.set(current_frame - 1)
                    # 更新時間標籤
                    current_time = (current_frame - 1) / fps if fps > 0 else 0
                    time_label.config(text=f"{str(timedelta(seconds=int(current_time)))} / {str(timedelta(seconds=int(duration)))}")
                    
                    # 更新畫面
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame)
                    imgtk = ImageTk.PhotoImage(image=img)
                    video_window.images.clear()
                    video_window.images.append(imgtk)
                    canvas.delete("all")
                    canvas.create_image(width//2, height//2, image=imgtk, anchor='center')

        # 後一幀按鈕功能
        def next_frame():
            current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            if current_frame < total_frames - 1:
                ret, frame = cap.read()
                if ret:
                    # 更新進度條
                    progress_var.set(current_frame)
                    # 更新時間標籤
                    current_time = current_frame / fps if fps > 0 else 0
                    time_label.config(text=f"{str(timedelta(seconds=int(current_time)))} / {str(timedelta(seconds=int(duration)))}")
                    
                    # 更新畫面
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame)
                    imgtk = ImageTk.PhotoImage(image=img)
                    video_window.images.clear()
                    video_window.images.append(imgtk)
                    canvas.delete("all")
                    canvas.create_image(width//2, height//2, image=imgtk, anchor='center')

        # 創建前一幀按鈕
        prev_frame_button = ttk.Button(control_frame, text="◀", command=previous_frame, width=3)
        prev_frame_button.pack(side='left', padx=2)

        # 創建後一幀按鈕
        next_frame_button = ttk.Button(control_frame, text="▶", command=next_frame, width=3)
        next_frame_button.pack(side='left', padx=2)

        # 當點擊前一幀或後一幀按鈕時，自動暫停播放
        def frame_control_click():
            nonlocal is_playing
            if is_playing:
                is_playing = False
                play_button.config(text="播放")

        prev_frame_button.config(command=lambda: [frame_control_click(), previous_frame()])
        next_frame_button.config(command=lambda: [frame_control_click(), next_frame()])

        # 開始播放
        update_frame()

class RTSPRecorderApp:
    def __init__(self, root):
        self.root = root

        # 獲取機器ID並設置標題
        auth = MachineAuthorization("machine.id")
        machine_id = auth.get_machine_id()
        self.root.title(f"NVRV-Free - 已授權 (ID: {machine_id[:8]}...)")  # 顯示部分機器ID

        self.root.geometry("1280x720")  # 設置初始視窗大小
        self.root.configure(bg=BACKGROUND_COLOR)  # 設置背景顏色
        
        # 設置應用程式圖標
        try:
            self.root.iconbitmap("icon.ico")  # 如果有圖標文件
        except:
            pass  # 如果沒有圖標文件，忽略錯誤
        
        # 設置關閉視窗協議
        self.root.protocol("WM_DELETE_WINDOW", self.close_app)
        
        # 創建樣式
        self.style = ttk.Style()
        self.style.theme_use('clam')  # 使用clam主題作為基礎
        
        # 配置樣式
        self.style.configure('TFrame', background=BACKGROUND_COLOR)
        self.style.configure('TLabel', background=BACKGROUND_COLOR, foreground=TEXT_COLOR)
        self.style.configure('TButton', background=THEME_COLOR, foreground='white')
        self.style.configure('TNotebook', background=BACKGROUND_COLOR)
        self.style.configure('TNotebook.Tab', background=BACKGROUND_COLOR, foreground=TEXT_COLOR, padding=[10, 5])
        self.style.map('TNotebook.Tab',
                      background=[('selected', THEME_COLOR)],
                      foreground=[('selected', 'white')])

        # 創建頂部控制區域
        self.control_frame = ttk.Frame(root)
        self.control_frame.pack(fill="x", padx=10, pady=10)

        # 添加標題標籤
        title_label = ttk.Label(self.control_frame, text="RTSP 網路錄影機系統", 
                              font=("Arial", 16, "bold"), foreground=THEME_COLOR)
        title_label.pack(side="left", padx=10)

        # 添加頻道數量調整控制項
        ttk.Label(self.control_frame, text="頻道數量:", font=("Arial", 10)).pack(side="left", padx=5)
        self.channel_count = tk.StringVar(value="1")  # 預設1個頻道
        self.channel_count_entry = ttk.Entry(self.control_frame, textvariable=self.channel_count, width=5)
        self.channel_count_entry.pack(side="left", padx=5)
        
        # 添加應用按鈕
        apply_button = ttk.Button(self.control_frame, text="應用變更", command=self.apply_channel_count)
        apply_button.pack(side="left", padx=5)
        
        # 創建一個Notebook (標籤頁)
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 創建主頻道頁面
        self.channels_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.channels_frame, text="頻道管理")
        
        # 創建說明頁面
        self.help_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.help_frame, text="使用說明")
        
        # 設置說明頁面內容
        self.setup_help_page()

        # 在頻道頁面創建可滾動區域
        container = ttk.Frame(self.channels_frame)
        container.pack(fill="both", expand=True)

        self.canvas = tk.Canvas(container, bg=BACKGROUND_COLOR)
        self.scrollbar = ttk.Scrollbar(container, orient="vertical", command=self.canvas.yview)
        self.frame = ttk.Frame(self.canvas, style='TFrame')

        self.frame.bind(
            "<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 初始化頻道列表
        self.channels = []
        
        # 關閉按鈕
        self.close_button = ttk.Button(root, text="關閉程式", command=self.close_app)
        self.close_button.pack(pady=10)

        # 載入設定並創建頻道
        self.load_settings()
        
        # 在程式啟動時清理所有頻道的舊檔案
        self.clean_all_old_directories()
        
        # 啟動預設定的頻道
        self.start_preconfigured_channels()
        
    def setup_help_page(self):
        """設置說明頁面內容"""
        # 創建滾動框架
        help_container = ttk.Frame(self.help_frame)
        help_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 創建畫布和滾動條
        help_canvas = tk.Canvas(help_container, bg=BACKGROUND_COLOR)
        help_scrollbar = ttk.Scrollbar(help_container, orient="vertical", command=help_canvas.yview)
        help_content = ttk.Frame(help_canvas)
        
        help_content.bind(
            "<Configure>", lambda e: help_canvas.configure(scrollregion=help_canvas.bbox("all"))
        )
        
        help_canvas.create_window((0, 0), window=help_content, anchor="nw")
        help_canvas.configure(yscrollcommand=help_scrollbar.set)
        
        help_canvas.pack(side="left", fill="both", expand=True)
        help_scrollbar.pack(side="right", fill="y")
        
        # 添加標題
        title = ttk.Label(help_content, text="RTSP 網路錄影機系統使用說明", 
                        font=("Arial", 16, "bold"), foreground=THEME_COLOR)
        title.pack(pady=10, anchor="w")
        
        # 添加版本資訊
        version = ttk.Label(help_content, text="版本: NVRV-Free 1.0", font=("Arial", 10))
        version.pack(pady=5, anchor="w")

        # 添加授權資訊
        auth = MachineAuthorization("machine.id")
        machine_id = auth.get_machine_id()

        auth_frame = ttk.Frame(help_content)
        auth_frame.pack(pady=5, fill="x")

        auth_label = ttk.Label(auth_frame, text="授權狀態: 已授權", font=("Arial", 10), foreground=ACCENT_COLOR)
        auth_label.pack(side="left")

        # 機器ID顯示框架
        id_frame = ttk.Frame(help_content)
        id_frame.pack(pady=5, fill="x")

        id_label = ttk.Label(id_frame, text="機器ID:", font=("Arial", 10))
        id_label.pack(side="left")

        id_entry = tk.Entry(id_frame, font=("Consolas", 9), width=35, state="readonly")
        id_entry.pack(side="left", padx=5)
        id_entry.config(state="normal")
        id_entry.insert(0, machine_id)
        id_entry.config(state="readonly")

        def copy_machine_id():
            self.root.clipboard_clear()
            self.root.clipboard_append(machine_id)
            copy_btn.config(text="已複製!")
            self.root.after(2000, lambda: copy_btn.config(text="複製"))

        copy_btn = ttk.Button(id_frame, text="複製", command=copy_machine_id, width=8)
        copy_btn.pack(side="left", padx=5)

        # 添加分隔線
        separator = ttk.Separator(help_content, orient="horizontal")
        separator.pack(fill="x", pady=10)
        
        # 基本功能說明
        basic_title = ttk.Label(help_content, text="基本功能", font=("Arial", 14, "bold"), foreground=THEME_COLOR)
        basic_title.pack(pady=5, anchor="w")
        
        basic_info = """
本系統提供多通道RTSP串流錄影功能，支持以下基本功能：

1. 多通道RTSP串流接收與錄影
2. 自定義頻道名稱和錄影參數
3. 自動清理過期錄影檔案
4. 錄影檔案播放與回放控制
5. 日期與時間搜尋功能
        """
        
        basic_text = tk.Text(help_content, wrap="word", height=10, width=80, 
                           font=("Arial", 10), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        basic_text.insert("1.0", basic_info)
        basic_text.config(state="disabled")
        basic_text.pack(pady=5, fill="x")
        
        # 使用說明
        usage_title = ttk.Label(help_content, text="使用說明", font=("Arial", 14, "bold"), foreground=THEME_COLOR)
        usage_title.pack(pady=5, anchor="w")
        
        usage_info = """
【頻道設定】
1. 點擊頻道上的「⚙」按鈕開啟設定面板
2. 輸入RTSP URL (例如: rtsp://admin:password@192.168.1.100:554/stream)
3. 設定頻道名稱、錄影時長、FPS、儲存路徑和保留天數
4. 點擊「Save Settings」儲存設定

【開始錄影】
1. 完成頻道設定後，點擊「Start」按鈕開始錄影
2. 錄影狀態會在預覽視窗下方顯示
3. 點擊「Stop」按鈕停止錄影

【播放錄影】
1. 點擊「Play」按鈕開啟日曆視窗
2. 選擇有錄影的日期(綠色標記)
3. 在檔案列表中選擇要播放的錄影檔案
4. 使用播放控制功能(播放/暫停、速度控制、時間跳轉等)

【系統設定】
1. 在主視窗頂部設定所需的頻道數量
2. 點擊「應用變更」按鈕更新頻道數量
        """
        
        usage_text = tk.Text(help_content, wrap="word", height=20, width=80, 
                           font=("Arial", 10), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        usage_text.insert("1.0", usage_info)
        usage_text.config(state="disabled")
        usage_text.pack(pady=5, fill="x")
        
        # 注意事項
        notes_title = ttk.Label(help_content, text="注意事項", font=("Arial", 14, "bold"), foreground=THEME_COLOR)
        notes_title.pack(pady=5, anchor="w")
        
        notes_info = """
1. 錄影檔案會依照日期自動分類儲存
2. 系統會自動依據「保留天數」設定清理過期檔案
3. 請確保儲存路徑有足夠的磁碟空間
4. 如需同時錄製大量頻道，請確保電腦效能足夠
5. 專業版支援最多50個頻道
        """
        
        notes_text = tk.Text(help_content, wrap="word", height=8, width=80, 
                           font=("Arial", 10), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        notes_text.insert("1.0", notes_info)
        notes_text.config(state="disabled")
        notes_text.pack(pady=5, fill="x")
        
        # 聯絡資訊
        contact_title = ttk.Label(help_content, text="聯絡與支援", font=("Arial", 14, "bold"), foreground=THEME_COLOR)
        contact_title.pack(pady=5, anchor="w")
        
        contact_frame = ttk.Frame(help_content)
        contact_frame.pack(pady=5, fill="x")
        
        contact_info = ttk.Label(contact_frame, text="如有任何問題或需要技術支援，請聯絡：", font=("Arial", 10))
        contact_info.pack(side="left", pady=5)
        
        # 添加網站連結
        def open_website():
            webbrowser.open("https://www.example.com")
            
        website_button = ttk.Button(contact_frame, text="訪問官方網站", command=open_website)
        website_button.pack(side="left", padx=10)

    def apply_channel_count(self):
        try:
            new_count = int(self.channel_count.get())
            if new_count < 1:
                messagebox.showerror("錯誤", "頻道數量必須大於0")
                return
            
            # 檢查是否超過專業版上限
            if new_count > 50:
                messagebox.showerror("錯誤", "專業版最多支援50個頻道")
                self.channel_count.set("50")
                return
                
            current_count = len(self.channels)
            
            # 顯示進度視窗
            progress_window = tk.Toplevel(self.root)
            progress_window.title("更新頻道數量")
            progress_window.geometry("300x150")
            progress_window.transient(self.root)
            progress_window.resizable(False, False)
            progress_window.configure(bg=BACKGROUND_COLOR)
            
            # 進度標籤
            progress_label = ttk.Label(progress_window, text="正在更新頻道數量...", font=("Arial", 10))
            progress_label.pack(pady=20)
            
            # 進度條
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()
            progress_window.update()
            
            # 新增頻道邏輯
            if new_count > current_count:
                for i in range(current_count, new_count):
                    # 創建新頻道並載入設定(如果存在)
                    channel = RTSPChannel(self.frame, i, self.root)
                    self.channels.append(channel)
                    # 載入新頻道的設定
                    self.load_single_channel_settings(i)
                    
                    # 更新進度視窗
                    progress_label.config(text=f"正在創建頻道 {i+1}/{new_count}")
                    progress_window.update()
                    
            # 減少頻道邏輯
            elif new_count < current_count:
                # 只移除未在錄影的頻道
                removable_channels = []
                recording_channels = []
                
                for idx in range(new_count, current_count):
                    channel = self.channels[idx]
                    if not channel.recording:
                        removable_channels.append(channel)
                    else:
                        recording_channels.append(channel)
                
                # 如果有正在錄影的頻道，顯示警告
                if recording_channels:
                    recording_names = [ch.channel_name.get() for ch in recording_channels]
                    warning_message = f"以下頻道正在錄影，無法移除：\n{', '.join(recording_names)}"
                    progress_window.destroy()
                    messagebox.showwarning("注意", warning_message)
                        
                # 更新實際可移除的數量
                actual_remove_count = len(removable_channels)
                if actual_remove_count < (current_count - new_count):
                    self.channel_count.set(str(current_count - actual_remove_count))
                    
                # 從後往前移除頻道
                for i, channel in enumerate(reversed(removable_channels)):
                    # 更新進度視窗
                    progress_label.config(text=f"正在移除頻道 {i+1}/{len(removable_channels)}")
                    progress_window.update()
                    
                    # 安全移除UI組件
                    channel.frame.grid_forget()
                    channel.frame.destroy()
                    self.channels.remove(channel)
                    
                # 重新排列剩餘頻道
                for i, channel in enumerate(self.channels):
                    channel.index = i
                    channel.frame.grid(row=i // 5, column=i % 5, padx=5, pady=5, sticky="nsew")
                    
            # 保存新設定
            self.save_settings()
            
            # 更新滾動區域
            self.frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
            # 關閉進度視窗
            progress_bar.stop()
            progress_window.destroy()
            
            messagebox.showinfo("成功", f"頻道數量已更新為 {len(self.channels)}")
            
        except ValueError:
            messagebox.showerror("錯誤", "請輸入有效的數字")
        except Exception as e:
            messagebox.showerror("錯誤", f"更新頻道數量時發生錯誤：{str(e)}")
            logging.error(f"Error updating channel count: {str(e)}")

    def load_single_channel_settings(self, index):
        """載入單一頻道設定"""
        try:
            if os.path.exists("settings_free.json"):
                with open("settings_free.json", "r", encoding='utf-8') as f:
                    data = json.load(f)
                    if index < len(data):
                        channel = self.channels[index]
                        channel_data = data[index]
                        channel.rtsp_url.set(channel_data.get("rtsp_url", ""))
                        channel.channel_name.set(channel_data.get("channel_name", f"Channel {index+1}"))
                        channel.record_duration.set(channel_data.get("record_duration", 20))
                        channel.save_path.set(channel_data.get("save_path", "./recordings"))
                        channel.keep_days.set(channel_data.get("keep_days", 7))
                        channel.record_fps.set(channel_data.get("record_fps", 8))
                        channel.use_source_fps.set(channel_data.get("use_source_fps", False))
        except Exception as e:
            logging.error(f"Error loading channel {index} settings: {str(e)}")

    def load_settings(self):
        try:
            if os.path.exists("settings_free.json"):
                with open("settings_free.json", "r", encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 更新頻道數量
                    channel_count = len(data)
                    self.channel_count.set(str(channel_count))
                    
                    # 確保有足夠的頻道實例
                    while len(self.channels) < channel_count:
                        channel = RTSPChannel(self.frame, len(self.channels), self.root)
                        self.channels.append(channel)
                    
                    # 載入每個頻道的設定
                    for i, channel_data in enumerate(data):
                        if i < len(self.channels):
                            channel = self.channels[i]
                            channel.rtsp_url.set(channel_data.get("rtsp_url", ""))
                            channel.channel_name.set(channel_data.get("channel_name", ""))
                            channel.record_duration.set(channel_data.get("record_duration", 20))
                            channel.save_path.set(channel_data.get("save_path", "./recordings"))
                            channel.keep_days.set(channel_data.get("keep_days", 7))
                            channel.record_fps.set(channel_data.get("record_fps", 8))
                            channel.use_source_fps.set(channel_data.get("use_source_fps", False))
            else:
                # 如果設定檔不存在，創建一個空的設定檔
                initial_count = 1  # 預設只創建1個頻道
                self.channel_count.set(str(initial_count))
                
                # 創建初始頻道
                channel = RTSPChannel(self.frame, 0, self.root)
                self.channels.append(channel)
                
                # 保存初始設定
                self.save_settings()
                
        except Exception as e:
            logging.error(f"Error loading settings: {str(e)}")
            messagebox.showerror("錯誤", f"載入設定檔時發生錯誤：{str(e)}")

    def save_settings(self):
        try:
            data = []
            for channel in self.channels:
                data.append({
                    "rtsp_url": channel.rtsp_url.get(),
                    "channel_name": channel.channel_name.get(),
                    "record_duration": channel.record_duration.get(),
                    "save_path": channel.save_path.get(),
                    "keep_days": channel.keep_days.get(),
                    "record_fps": channel.record_fps.get(),
                    "use_source_fps": channel.use_source_fps.get(),
                })
            with open("settings_free.json", "w", encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Error saving settings: {str(e)}")
            messagebox.showerror("錯誤", f"保存設定檔時發生錯誤：{str(e)}")

    def start_preconfigured_channels(self):
        for channel in self.channels:
            if channel.rtsp_url.get():
                channel.start_recording()

    def close_app(self):
        try:
            # 檢查是否有正在錄影的頻道
            recording_channels = [channel for channel in self.channels if channel.recording]
            if recording_channels:
                # 詢問用戶是否要停止錄影
                if messagebox.askyesno("確認關閉", 
                                     "有頻道正在錄影中。是否停止錄影並關閉程式？"):
                    # 顯示進度窗口
                    progress_window = tk.Toplevel(self.root)
                    progress_window.title("停止錄影中")
                    progress_window.geometry("400x200")
                    progress_window.transient(self.root)
                    progress_window.configure(bg=BACKGROUND_COLOR)
                    progress_window.resizable(False, False)
                    
                    # 添加標題
                    title_label = ttk.Label(progress_window, 
                                          text="正在停止錄影，請稍候...", 
                                          font=("Arial", 12, "bold"),
                                          foreground=THEME_COLOR)
                    title_label.pack(pady=15)
                    
                    # 進度標籤
                    progress_label = ttk.Label(progress_window, 
                                             text="準備停止錄影...",
                                             font=("Arial", 10))
                    progress_label.pack(pady=10)
                    
                    # 進度條
                    progress_bar = ttk.Progressbar(progress_window, 
                                                 mode='determinate',
                                                 maximum=len(recording_channels))
                    progress_bar.pack(pady=10, padx=30, fill='x')
                    
                    # 顯示剩餘頻道數
                    count_label = ttk.Label(progress_window, 
                                          text=f"剩餘: {len(recording_channels)} 個頻道",
                                          font=("Arial", 9))
                    count_label.pack(pady=5)
                    
                    # 依序停止各個頻道錄影
                    for i, channel in enumerate(recording_channels):
                        try:
                            channel_name = channel.channel_name.get()
                            progress_label.config(text=f"正在停止: {channel_name}")
                            count_label.config(text=f"剩餘: {len(recording_channels) - i} 個頻道")
                            progress_bar['value'] = i
                            progress_window.update()
                            
                            # 停止錄影
                            channel.stop_recording()
                            
                            # 等待確保檔案被保存
                            timeout = time.time() + 5  # 5秒超時
                            while channel.out is not None and time.time() < timeout:
                                time.sleep(0.1)
                                progress_window.update()
                                
                        except Exception as e:
                            logging.error(f"Error stopping channel {channel_name}: {e}")
                    
                    progress_bar['value'] = len(recording_channels)
                    progress_label.config(text="所有錄影已停止")
                    count_label.config(text="完成")
                    progress_window.update()
                    
                    # 再次確認所有通道都停止了
                    time.sleep(1)  # 等待最後的保存操作
                    still_recording = [ch for ch in self.channels if ch.recording]
                    if still_recording:
                        messagebox.showerror("錯誤", 
                                           "部分頻道無法停止錄影。請再試一次。")
                        progress_window.destroy()
                        return
                    
                    # 顯示完成訊息
                    progress_label.config(text="正在儲存設定...")
                    progress_window.update()
                    
                    # 保存設置
                    try:
                        self.save_settings()
                        progress_label.config(text="設定已儲存，準備關閉程式...")
                        progress_window.update()
                        time.sleep(1)
                    except Exception as e:
                        logging.error(f"Error saving settings: {e}")
                    
                    progress_window.destroy()
                else:
                    # 用戶選擇取消
                    return
            else:
                # 如果沒有正在錄影的頻道，直接保存設定
                try:
                    self.save_settings()
                except Exception as e:
                    logging.error(f"Error saving settings: {e}")
            
            # 最後關閉程式
            self.root.quit()
            self.root.destroy()
            
        except Exception as e:
            logging.error(f"Error during shutdown: {e}")
            messagebox.showerror("錯誤", f"關閉程式時發生錯誤：{str(e)}")

    def clean_all_old_directories(self):
        """清理所有頻道的舊檔案"""
        for channel in self.channels:
            try:
                channel.clean_old_directories()
            except Exception as e:
                logging.error(f"Error cleaning old directories for channel {channel.channel_name.get()}: {e}")

def check_machine_license():
    """檢查機器授權"""
    try:
        # 創建臨時根視窗用於授權檢查對話框
        temp_root = tk.Tk()
        temp_root.withdraw()  # 隱藏主視窗

        # 進行授權檢查
        auth = MachineAuthorization("machine.id")
        is_authorized = auth.check_authorization(show_gui=True, exit_on_fail=False)

        # 銷毀臨時視窗
        temp_root.destroy()

        return is_authorized
    except Exception as e:
        print(f"授權檢查過程中發生錯誤: {e}")
        return False


if __name__ == "__main__":
    # 首先進行機器授權檢查
    print("NVRV-Free 正在啟動...")
    print("正在進行機器授權驗證...")

    if not check_machine_license():
        print("授權驗證失敗，程式將退出。")
        sys.exit(1)

    print("授權驗證通過，正在啟動主程式...")

    # 授權通過後創建主程式
    root = tk.Tk()
    app = RTSPRecorderApp(root)
    root.mainloop()
