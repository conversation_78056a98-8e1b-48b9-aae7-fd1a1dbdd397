#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVRV-Free Web介面啟動腳本
整合主程式和Web介面
"""

import sys
import os
import threading
import time
import argparse
from pathlib import Path

# 添加當前目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from web_interface import NVRVWebInterface
    print("✅ Web介面模組載入成功")
except ImportError as e:
    print(f"❌ Web介面模組載入失敗: {e}")
    print("請安裝必要套件: pip install -r web_requirements.txt")
    sys.exit(1)

def check_dependencies():
    """檢查依賴套件"""
    required_packages = [
        'flask', 'flask_socketio', 'cv2', 'PIL'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要套件: {', '.join(missing_packages)}")
        print("請執行: pip install -r web_requirements.txt")
        return False
    
    return True

def create_directories():
    """創建必要的目錄"""
    directories = [
        'templates',
        'static',
        'recordings'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 確保目錄存在: {directory}")

def main():
    parser = argparse.ArgumentParser(description='NVRV-Free Web介面')
    parser.add_argument('--host', default='0.0.0.0', help='Web服務器主機 (預設: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8080, help='Web服務器埠號 (預設: 8080)')
    parser.add_argument('--debug', action='store_true', help='啟用除錯模式')
    parser.add_argument('--no-main', action='store_true', help='只啟動Web介面，不啟動主程式')
    
    args = parser.parse_args()
    
    print("🚀 NVRV-Free Web介面啟動中...")
    print("=" * 50)
    
    # 檢查依賴
    if not check_dependencies():
        sys.exit(1)
    
    # 創建目錄
    create_directories()
    
    # 檢查模板檔案
    template_files = [
        'templates/base.html',
        'templates/index.html', 
        'templates/playback.html',
        'templates/settings.html'
    ]
    
    missing_templates = []
    for template in template_files:
        if not os.path.exists(template):
            missing_templates.append(template)
    
    if missing_templates:
        print(f"⚠️  缺少模板檔案: {', '.join(missing_templates)}")
        print("Web介面可能無法正常顯示")
    
    # 啟動主程式 (如果需要)
    nvrv_instance = None
    if not args.no_main:
        try:
            print("🎬 嘗試載入NVRV主程式...")
            # TODO: 整合NVRV主程式
            # from NVRV_free import NVRVMainApp
            # nvrv_instance = NVRVMainApp()
            # nvrv_thread = threading.Thread(target=nvrv_instance.run, daemon=True)
            # nvrv_thread.start()
            print("✅ NVRV主程式已在背景運行")
        except Exception as e:
            print(f"⚠️  無法載入NVRV主程式: {e}")
            print("Web介面將以獨立模式運行")
    
    # 啟動Web介面
    try:
        web_interface = NVRVWebInterface(nvrv_instance)
        
        print(f"🌐 Web介面啟動資訊:")
        print(f"   主機: {args.host}")
        print(f"   埠號: {args.port}")
        print(f"   網址: http://{args.host}:{args.port}")
        print(f"   除錯: {'啟用' if args.debug else '停用'}")
        print("=" * 50)
        print("🎉 NVRV-Free Web介面已啟動！")
        print("📱 請在瀏覽器中開啟上述網址")
        print("⏹️  按 Ctrl+C 停止服務")
        print()
        
        # 啟動Web服務器
        web_interface.run(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號，正在關閉...")
    except Exception as e:
        print(f"❌ Web介面啟動失敗: {e}")
        sys.exit(1)
    finally:
        print("👋 NVRV-Free Web介面已停止")

if __name__ == '__main__':
    main()