# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(['start_web_server.py'],
             pathex=['C:/Users/<USER>/Desktop/v1'], # 使用正斜槓避免轉義問題
             binaries=[
                 # 這裡可以添加OpenCV的ffmpeg DLLs，如果PyInstaller自動偵測不到
                 # 例如: ('C:/path/to/your/python/Lib/site-packages/cv2/opencv_ffmpeg455_64.dll', '.'),
                 # 請根據您的OpenCV版本和安裝路徑調整
             ],
             datas=[
                 ('templates', 'templates'),
                 ('static', 'static'),
                 ('settings_free.json', '.'),
                 ('key.ico', '.')
             ],
             hiddenimports=[
                'flask_socketio.async_mode_threading',
                'babel.numbers',
                'babel.dates',
                'pystray',
                'PIL.Image',
                'PIL.ImageDraw',
                'PIL.ImageFont',
                'PIL._tkinter_finder',
                'cv2',
                'sqlite3',
                'werkzeug.security',
                'webbrowser',
                'threading',
                'json',
                'logging',
                'os',
                'sys',
                'time',
                'datetime',
                'base64',
                'glob',
                'pathlib',
                'mimetypes',
                'numpy',
                'werkzeug.serving',
                'flask_login',
                'functools',
             ],
             hookspath=[],
             hooksconfig={},
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)

exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          [],
          name='NVR_System',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          upx_exclude=[],
          runtime_tmpdir=None,
          console=False,
          disable_windowed_traceback=False,
          target_arch=None,
          codesign_identity=None,
          entitlements_file=None,
          icon='key.ico')