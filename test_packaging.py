#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包配置測試腳本
"""

import os
import sys
import importlib

def test_imports():
    """測試所有必要的模組是否可以導入"""
    required_modules = [
        'flask',
        'flask_socketio', 
        'flask_login',
        'cv2',
        'PIL',
        'pystray',
        'numpy',
        'sqlite3',
        'babel.numbers',
        'babel.dates',
        'werkzeug.security',
        'werkzeug.serving',
        'eventlet',
        'gevent',
        'socketio',
        'engineio'
    ]
    
    print("🔍 測試模組導入...")
    failed_modules = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - {e}")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"\n❌ 缺失的模組: {failed_modules}")
        return False
    else:
        print("\n✅ 所有模組導入成功")
        return True

def test_files():
    """測試必要文件是否存在"""
    required_files = [
        'start_web_server.py',
        'nvrv_web_server.py',
        'settings_free.json',
        'web_server.spec',
        'key.ico'
    ]
    
    required_dirs = [
        'templates'
    ]
    
    print("\n📁 測試必要文件...")
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 缺失")
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - 缺失")
            missing_files.append(dir_path)
    
    if missing_files:
        print(f"\n❌ 缺失的文件: {missing_files}")
        return False
    else:
        print("\n✅ 所有必要文件都存在")
        return True

def test_templates():
    """測試模板文件"""
    template_files = [
        'templates/base.html',
        'templates/index.html',
        'templates/login.html',
        'templates/playback.html'
    ]
    
    print("\n📄 測試模板文件...")
    missing_templates = []
    
    for template in template_files:
        if os.path.exists(template):
            print(f"✅ {template}")
        else:
            print(f"❌ {template} - 缺失")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n❌ 缺失的模板: {missing_templates}")
        return False
    else:
        print("\n✅ 所有模板文件都存在")
        return True

def main():
    print("=" * 50)
    print("NVRV-Free Web Server 打包配置測試")
    print("=" * 50)
    
    # 測試模組導入
    imports_ok = test_imports()
    
    # 測試文件
    files_ok = test_files()
    
    # 測試模板
    templates_ok = test_templates()
    
    print("\n" + "=" * 50)
    if imports_ok and files_ok and templates_ok:
        print("🎉 所有測試通過！可以開始打包")
        print("\n💡 執行以下命令開始打包:")
        print("   build_web_server.bat")
        print("   或")
        print("   pyinstaller --clean web_server.spec")
    else:
        print("❌ 測試失敗！請解決上述問題後再打包")
        print("\n🔧 常見解決方案:")
        print("1. 安裝缺失的模組: pip install -r web_server_requirements.txt")
        print("2. 確保所有必要文件都在正確位置")
        print("3. 檢查 Python 版本 (建議 3.8+)")
    
    print("=" * 50)

if __name__ == '__main__':
    main() 