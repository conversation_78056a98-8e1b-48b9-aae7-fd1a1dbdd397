#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
幀率分析工具
用於分析RTSP串流的幀率穩定性
"""

import cv2
import time
import sys
from collections import deque
import threading

class FramerateAnalyzer:
    def __init__(self, rtsp_url):
        self.rtsp_url = rtsp_url
        self.frame_times = deque(maxlen=100)  # 保存最近100幀的時間
        self.duplicate_count = 0
        self.total_frames = 0
        self.last_frame_hash = None
        self.running = False
        
    def calculate_frame_hash(self, frame):
        """計算幀的簡單雜湊值"""
        try:
            import hashlib
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            small = cv2.resize(gray, (32, 24))
            return hashlib.md5(small.tobytes()).hexdigest()
        except:
            return None
    
    def analyze_stream(self, duration_seconds=30):
        """分析串流幀率"""
        print(f"🔍 分析RTSP串流: {self.rtsp_url}")
        print(f"⏱️  分析時間: {duration_seconds} 秒")
        print("=" * 50)
        
        cap = cv2.VideoCapture(self.rtsp_url)
        if not cap.isOpened():
            print("❌ 無法連接到RTSP串流")
            return
        
        # 獲取串流信息
        source_fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📺 串流信息:")
        print(f"   解析度: {width}x{height}")
        print(f"   宣告FPS: {source_fps}")
        print()
        
        start_time = time.time()
        self.running = True
        
        # 啟動統計顯示線程
        stats_thread = threading.Thread(target=self.show_realtime_stats, daemon=True)
        stats_thread.start()
        
        while self.running and (time.time() - start_time) < duration_seconds:
            ret, frame = cap.read()
            if not ret:
                print("⚠️  讀取幀失敗")
                time.sleep(0.1)
                continue
            
            current_time = time.time()
            self.frame_times.append(current_time)
            self.total_frames += 1
            
            # 檢測重複幀
            current_hash = self.calculate_frame_hash(frame)
            if current_hash == self.last_frame_hash:
                self.duplicate_count += 1
            self.last_frame_hash = current_hash
        
        self.running = False
        cap.release()
        
        # 最終統計
        self.show_final_stats(duration_seconds)
    
    def show_realtime_stats(self):
        """顯示即時統計"""
        while self.running:
            if len(self.frame_times) >= 2:
                # 計算當前FPS
                time_span = self.frame_times[-1] - self.frame_times[0]
                if time_span > 0:
                    current_fps = (len(self.frame_times) - 1) / time_span
                    duplicate_rate = (self.duplicate_count / max(1, self.total_frames)) * 100
                    
                    print(f"\r📊 即時統計 - FPS: {current_fps:.2f} | 總幀數: {self.total_frames} | 重複率: {duplicate_rate:.1f}%", end="")
            
            time.sleep(1)
    
    def show_final_stats(self, duration):
        """顯示最終統計"""
        print("\n\n" + "=" * 50)
        print("📊 最終分析結果:")
        
        if len(self.frame_times) >= 2:
            total_time = self.frame_times[-1] - self.frame_times[0]
            average_fps = (len(self.frame_times) - 1) / total_time if total_time > 0 else 0
            
            print(f"⏱️  實際分析時間: {total_time:.2f} 秒")
            print(f"🎬 總接收幀數: {self.total_frames}")
            print(f"📈 平均FPS: {average_fps:.2f}")
            print(f"🔄 重複幀數: {self.duplicate_count}")
            print(f"📊 重複幀比例: {(self.duplicate_count/max(1,self.total_frames))*100:.1f}%")
            
            # 計算FPS穩定性
            if len(self.frame_times) >= 10:
                intervals = []
                for i in range(1, len(self.frame_times)):
                    interval = self.frame_times[i] - self.frame_times[i-1]
                    intervals.append(interval)
                
                avg_interval = sum(intervals) / len(intervals)
                variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                stability = max(0, 100 - (variance * 1000))  # 簡單的穩定性指標
                
                print(f"📊 FPS穩定性: {stability:.1f}% (100%=完全穩定)")
            
            print()
            print("💡 建議:")
            
            if average_fps > 15:
                print("- FPS過高，建議在NVRV-Free中設定為8fps")
            
            if self.duplicate_count / max(1, self.total_frames) > 0.3:
                print("- 重複幀比例過高，智能幀率控制會大幅節省空間")
            
            if average_fps < 5:
                print("- FPS過低，可能影響錄影品質")
            
            # 預估檔案大小
            effective_fps = min(8, average_fps * (1 - self.duplicate_count/max(1,self.total_frames)))
            estimated_mb_per_min = effective_fps * 0.3  # 粗略估算
            print(f"- 預估檔案大小: {estimated_mb_per_min:.1f} MB/分鐘 (使用智能控制)")

def main():
    print("NVRV-Free RTSP幀率分析工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        rtsp_url = sys.argv[1]
    else:
        rtsp_url = input("請輸入RTSP URL: ").strip()
    
    if not rtsp_url:
        print("❌ 未提供RTSP URL")
        return
    
    duration = 30
    try:
        duration_input = input(f"分析時間 (秒，預設{duration}): ").strip()
        if duration_input:
            duration = int(duration_input)
    except:
        pass
    
    analyzer = FramerateAnalyzer(rtsp_url)
    
    try:
        analyzer.analyze_stream(duration)
    except KeyboardInterrupt:
        print("\n\n⏹️  分析被用戶中斷")
        analyzer.running = False
    except Exception as e:
        print(f"\n\n❌ 分析過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()