@echo off
chcp 65001 >nul
title NVRV-Free Web服務器

echo.
echo ========================================
echo    NVRV-Free Web介面服務器啟動器
echo ========================================
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：未找到Python，請先安裝Python 3.7+
    pause
    exit /b 1
)

REM 檢查必要的Python套件
echo 🔍 檢查必要套件...
python -c "import flask, flask_socketio, cv2, numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要套件，正在安裝...
    pip install flask flask-socketio opencv-python numpy
    if errorlevel 1 (
        echo ❌ 套件安裝失敗，請手動執行：
        echo    pip install flask flask-socketio opencv-python numpy
        pause
        exit /b 1
    )
)

REM 檢查配置文件
if not exist "settings_free.json" (
    echo ⚠️  未找到配置文件，是否創建演示配置？
    set /p choice="輸入 y 創建演示配置，或 n 退出: "
    if /i "!choice!"=="y" (
        python create_demo_config.py
        if errorlevel 1 (
            echo ❌ 創建演示配置失敗
            pause
            exit /b 1
        )
    ) else (
        echo 👋 請先準備配置文件後再啟動
        pause
        exit /b 0
    )
)

echo.
echo 🚀 啟動Web服務器...
echo 📡 服務地址: http://localhost:8080
echo 🌐 區域網路: http://%COMPUTERNAME%:8080
echo.
echo 按 Ctrl+C 停止服務器
echo ========================================
echo.

REM 啟動Web服務器
python start_web_server.py

echo.
echo 👋 服務器已停止
pause