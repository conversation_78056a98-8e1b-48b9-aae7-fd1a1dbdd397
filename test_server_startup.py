#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試服務器啟動的簡單腳本
"""

import sys
import os
import time
import requests
import subprocess

def test_exe_startup():
    """測試 EXE 啟動"""
    exe_path = "dist/NVRV_WebServer.exe"
    
    if not os.path.exists(exe_path):
        print("❌ EXE 檔案不存在")
        return False
    
    print("🚀 啟動 EXE...")
    process = subprocess.Popen([exe_path])
    
    # 等待啟動
    print("⏳ 等待服務器啟動...")
    for i in range(10):
        time.sleep(1)
        try:
            response = requests.get("http://localhost:8080", timeout=2)
            if response.status_code == 200:
                print("✅ 服務器啟動成功！")
                return True
        except:
            print(f"   嘗試 {i+1}/10...")
    
    print("❌ 服務器啟動失敗")
    
    # 檢查錯誤檔案
    error_files = ['startup_error.txt', 'config_error.txt', 'nvrv_web_server.log']
    for error_file in error_files:
        if os.path.exists(error_file):
            print(f"\n📄 錯誤檔案 {error_file}:")
            with open(error_file, 'r', encoding='utf-8') as f:
                print(f.read())
    
    return False

if __name__ == '__main__':
    test_exe_startup()