#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查Web介面依賴套件
"""

import sys

def check_package(package_name, import_name=None):
    """檢查套件是否已安裝"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} - 已安裝")
        return True
    except ImportError:
        print(f"❌ {package_name} - 未安裝")
        return False

def main():
    print("🔍 檢查Web介面依賴套件...")
    print("=" * 50)
    
    required_packages = [
        ('Flask', 'flask'),
        ('Flask-SocketIO', 'flask_socketio'),
        ('OpenCV', 'cv2'),
        ('Pillow', 'PIL'),
        ('python-socketio', 'socketio'),
        ('eventlet', 'eventlet'),
        ('requests', 'requests')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    print("\n📊 檢查結果:")
    print("=" * 50)
    
    if missing_packages:
        print(f"❌ 缺少 {len(missing_packages)} 個套件:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print("\n💡 安裝命令:")
        print("pip install flask flask-socketio opencv-python pillow python-socketio eventlet requests")
        
        return False
    else:
        print("✅ 所有依賴套件都已安裝！")
        return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)