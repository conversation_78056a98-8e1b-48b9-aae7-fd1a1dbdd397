#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
H.264優化效果測試工具
比較啟用和未啟用CABAC+GOP優化的效果
"""

import cv2
import numpy as np
import os
import time
from datetime import datetime

def create_test_video(codec_name, use_optimization=False, duration_seconds=30):
    """創建測試影片"""
    width, height = 640, 360
    fps = 8
    total_frames = fps * duration_seconds
    
    timestamp = int(time.time())
    opt_suffix = "_optimized" if use_optimization else "_basic"
    filename = f"test_{codec_name}{opt_suffix}_{timestamp}.mp4"
    
    print(f"🎬 創建測試影片: {filename}")
    print(f"   編碼器: {codec_name}")
    print(f"   優化: {'啟用' if use_optimization else '未啟用'}")
    print(f"   時長: {duration_seconds} 秒")
    
    # 創建VideoWriter
    fourcc = cv2.VideoWriter_fourcc(*codec_name)
    writer = cv2.VideoWriter(filename, fourcc, fps, (width, height))
    
    if not writer.isOpened():
        print(f"❌ 無法創建VideoWriter")
        return None, 0
    
    # 基本設定
    try:
        writer.set(cv2.VIDEOWRITER_PROP_QUALITY, 30)
        writer.set(cv2.VIDEOWRITER_PROP_BITRATE, 400000)
    except:
        pass
    
    # H.264優化設定
    if use_optimization and codec_name in ['H264', 'avc1', 'X264']:
        try:
            # 啟用硬體加速
            writer.set(cv2.VIDEOWRITER_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        except:
            pass
        
        try:
            # GOP設定
            gop_size = min(48, int(fps * 6))
            writer.set(cv2.VIDEOWRITER_PROP_FRAME_COUNT, gop_size)
        except:
            pass
        
        try:
            # H.264高級參數
            h264_params = (
                "cabac=1:ref=2:deblock=1,0,0:analyse=0x1:0x111:me=hex:subme=6:"
                "psy=1:psy_rd=1.0,0.0:mixed_ref=1:me_range=16:chroma_me=1:"
                "trellis=1:8x8dct=1:bframes=2:b_pyramid=2:b_adapt=1:"
                f"keyint={min(48, int(fps*6))}:keyint_min={int(fps)}:"
                "rc=crf:crf=28:aq=1:aq_strength=1.0"
            )
            writer.set(cv2.VIDEOWRITER_PROP_CODEC_PARAMS, h264_params)
            print(f"   ✅ 應用H.264高級參數")
        except Exception as e:
            print(f"   ⚠️  高級參數不支援: {e}")
    
    # 生成測試內容
    start_time = time.time()
    
    for i in range(total_frames):
        # 創建動態測試內容
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 背景漸變
        for y in range(height):
            for x in range(width):
                frame[y, x, 0] = int((x / width) * 255)  # 藍色漸變
                frame[y, x, 1] = int((y / height) * 255)  # 綠色漸變
                frame[y, x, 2] = int(((i % 60) / 60) * 255)  # 紅色動畫
        
        # 添加移動的圓形（模擬監控中的移動物體）
        center_x = int((width / 2) + 100 * np.sin(i * 0.1))
        center_y = int((height / 2) + 50 * np.cos(i * 0.1))
        cv2.circle(frame, (center_x, center_y), 30, (255, 255, 255), -1)
        
        # 添加文字信息
        cv2.putText(frame, f"Frame {i+1}/{total_frames}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        cv2.putText(frame, f"Codec: {codec_name}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        cv2.putText(frame, f"Opt: {'ON' if use_optimization else 'OFF'}", 
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        # 添加時間戳
        timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        cv2.putText(frame, timestamp_str, 
                   (10, height-10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
        
        writer.write(frame)
        
        # 進度顯示
        if (i + 1) % (total_frames // 10) == 0:
            progress = ((i + 1) / total_frames) * 100
            print(f"   📊 進度: {progress:.0f}%")
    
    encoding_time = time.time() - start_time
    writer.release()
    
    # 檢查結果
    if os.path.exists(filename):
        file_size = os.path.getsize(filename)
        print(f"   ✅ 完成! 檔案大小: {file_size/1024/1024:.2f} MB")
        print(f"   ⏱️  編碼時間: {encoding_time:.2f} 秒")
        return filename, file_size
    else:
        print(f"   ❌ 檔案創建失敗")
        return None, 0

def compare_optimization_effect():
    """比較優化效果"""
    print("H.264 CABAC+GOP 優化效果測試")
    print("=" * 50)
    
    # 測試的編碼器
    codecs_to_test = [
        ('H264', 'H.264編碼器'),
        ('avc1', 'AVC1編碼器'),
        ('X264', 'X264編碼器')
    ]
    
    results = []
    
    for codec_name, codec_desc in codecs_to_test:
        print(f"\n🧪 測試 {codec_desc}")
        print("-" * 30)
        
        # 測試基本版本
        basic_file, basic_size = create_test_video(codec_name, use_optimization=False)
        
        # 測試優化版本
        optimized_file, optimized_size = create_test_video(codec_name, use_optimization=True)
        
        if basic_size > 0 and optimized_size > 0:
            # 計算節省效果
            savings = ((basic_size - optimized_size) / basic_size) * 100
            
            print(f"\n📊 {codec_desc} 結果:")
            print(f"   基本版本: {basic_size/1024/1024:.2f} MB")
            print(f"   優化版本: {optimized_size/1024/1024:.2f} MB")
            print(f"   節省空間: {savings:.1f}%")
            
            results.append({
                'codec': codec_desc,
                'basic_size': basic_size,
                'optimized_size': optimized_size,
                'savings': savings,
                'basic_file': basic_file,
                'optimized_file': optimized_file
            })
            
            # 清理測試檔案
            if basic_file and os.path.exists(basic_file):
                os.remove(basic_file)
            if optimized_file and os.path.exists(optimized_file):
                os.remove(optimized_file)
        else:
            print(f"   ❌ {codec_desc} 測試失敗")
    
    # 總結報告
    print("\n" + "=" * 50)
    print("📊 優化效果總結")
    print("=" * 50)
    
    if results:
        total_basic = sum(r['basic_size'] for r in results)
        total_optimized = sum(r['optimized_size'] for r in results)
        average_savings = sum(r['savings'] for r in results) / len(results)
        
        print(f"測試編碼器數量: {len(results)}")
        print(f"平均節省空間: {average_savings:.1f}%")
        print(f"總檔案大小 (基本): {total_basic/1024/1024:.2f} MB")
        print(f"總檔案大小 (優化): {total_optimized/1024/1024:.2f} MB")
        
        print(f"\n各編碼器詳細結果:")
        for result in results:
            print(f"  {result['codec']}: 節省 {result['savings']:.1f}%")
        
        print(f"\n💡 結論:")
        if average_savings > 15:
            print(f"✅ CABAC+GOP優化效果顯著，建議啟用")
        elif average_savings > 5:
            print(f"⚠️  CABAC+GOP優化有一定效果")
        else:
            print(f"❌ CABAC+GOP優化效果不明顯，可能不支援")
    else:
        print("❌ 沒有成功的測試結果")

def main():
    print("NVRV-Free H.264優化效果測試工具")
    print("=" * 40)
    
    try:
        compare_optimization_effect()
    except KeyboardInterrupt:
        print("\n\n⏹️  測試被用戶中斷")
    except Exception as e:
        print(f"\n\n❌ 測試過程中發生錯誤: {e}")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()