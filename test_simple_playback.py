#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試簡化後的播放功能
"""

import requests
import json
import time
import subprocess
import sys
import os
import threading

def start_web_server_background():
    """在背景啟動 Web 服務器"""
    try:
        process = subprocess.Popen(
            [sys.executable, 'start_web_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return process
    except Exception as e:
        print(f"啟動 Web 服務器失敗: {e}")
        return None

def wait_for_server(max_attempts=15):
    """等待服務器啟動"""
    for i in range(max_attempts):
        try:
            response = requests.get("http://localhost:8080/", timeout=2)
            if response.status_code == 200:
                return True
        except:
            time.sleep(1)
            print(f"⏳ 等待服務器啟動... ({i+1}/{max_attempts})")
    return False

def test_video_serving():
    """測試影片服務功能"""
    print("=== 測試影片服務功能 ===")
    
    base_url = "http://localhost:8080"
    
    try:
        # 獲取頻道列表
        response = requests.get(f"{base_url}/api/channels", timeout=10)
        if response.status_code != 200:
            print(f"❌ 頻道 API 失敗: {response.status_code}")
            return False
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ 頻道 API 錯誤: {data.get('error')}")
            return False
        
        channels = data.get('channels', [])
        print(f"✅ 載入 {len(channels)} 個頻道")
        
        # 測試前幾個有錄影的頻道
        test_count = 0
        success_count = 0
        
        for channel in channels[:10]:  # 測試前10個頻道
            channel_id = channel.get('id')
            channel_name = channel.get('name')
            
            # 獲取錄影列表
            today = "20250731"
            response = requests.get(
                f"{base_url}/api/recordings?channel_id={channel_id}&date={today}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    recordings = data.get('recordings', [])
                    
                    if recordings:
                        test_count += 1
                        print(f"📹 測試頻道 {channel_id} ({channel_name}): {len(recordings)} 個錄影")
                        
                        # 測試播放第一個影片
                        test_filename = recordings[0]['filename']
                        file_size = recordings[0].get('size', 0)
                        
                        print(f"   🎬 測試檔案: {test_filename} ({file_size/1024/1024:.1f} MB)")
                        
                        try:
                            # 測試影片 URL 是否可訪問
                            video_url = f"{base_url}/api/video/{test_filename}"
                            response = requests.head(video_url, timeout=15)
                            
                            if response.status_code == 200:
                                content_length = response.headers.get('Content-Length', '0')
                                content_type = response.headers.get('Content-Type', 'unknown')
                                
                                print(f"   ✅ 影片可訪問: {content_type}")
                                print(f"      Content-Length: {content_length} bytes")
                                
                                # 測試下載前 1KB 數據
                                response = requests.get(
                                    video_url,
                                    headers={'Range': 'bytes=0-1023'},
                                    timeout=10
                                )
                                
                                if response.status_code in [200, 206]:
                                    print(f"   ✅ 影片串流正常: 收到 {len(response.content)} bytes")
                                    success_count += 1
                                else:
                                    print(f"   ❌ 影片串流失敗: {response.status_code}")
                            else:
                                print(f"   ❌ 影片無法訪問: {response.status_code}")
                                
                        except Exception as e:
                            print(f"   ❌ 測試播放失敗: {e}")
                        
                        print()  # 空行分隔
                        
                        # 只測試前3個有錄影的頻道
                        if test_count >= 3:
                            break
        
        print(f"📊 測試結果: {success_count}/{test_count} 個頻道播放正常")
        
        if success_count > 0:
            print("🎉 播放功能基本正常！")
            return True
        else:
            print("❌ 所有測試都失敗了")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_web_interface():
    """測試 Web 介面訪問"""
    print("=== 測試 Web 介面訪問 ===")
    
    try:
        # 測試主頁
        response = requests.get("http://localhost:8080/", timeout=5)
        if response.status_code == 200:
            print("✅ 主頁可訪問")
        else:
            print(f"❌ 主頁訪問失敗: {response.status_code}")
            return False
        
        # 測試播放頁面
        response = requests.get("http://localhost:8080/playback", timeout=5)
        if response.status_code == 200:
            print("✅ 播放頁面可訪問")
            
            # 檢查頁面內容
            content = response.text
            if 'videoPlayer' in content:
                print("✅ 播放器元素存在")
            else:
                print("⚠️  播放器元素可能缺失")
            
            if 'shareVideo' not in content:
                print("✅ 分享功能已移除")
            else:
                print("⚠️  分享功能可能仍存在")
            
            return True
        else:
            print(f"❌ 播放頁面訪問失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web 介面測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("NVRV-Free 簡化播放功能測試")
    print("=" * 50)
    
    # 檢查是否已有服務器運行
    server_process = None
    try:
        response = requests.get("http://localhost:8080/", timeout=3)
        print("✅ Web 服務器已在運行")
    except:
        print("🚀 啟動 Web 服務器...")
        server_process = start_web_server_background()
        if not server_process:
            print("❌ 無法啟動 Web 服務器")
            return 1
        
        if not wait_for_server():
            print("❌ Web 服務器啟動超時")
            if server_process:
                server_process.terminate()
            return 1
        
        print("✅ Web 服務器啟動成功")
    
    try:
        # 測試 Web 介面
        web_ok = test_web_interface()
        
        # 測試影片服務
        video_ok = test_video_serving()
        
        print("\n" + "=" * 50)
        print("📋 測試總結:")
        
        if web_ok:
            print("✅ Web 介面正常")
        else:
            print("❌ Web 介面有問題")
        
        if video_ok:
            print("✅ 影片播放功能正常")
        else:
            print("❌ 影片播放功能有問題")
        
        if web_ok and video_ok:
            print("\n🎉 所有功能正常！")
            print("📝 使用說明:")
            print("1. 開啟瀏覽器訪問: http://localhost:8080/playback")
            print("2. 選擇有錄影的頻道")
            print("3. 選擇日期")
            print("4. 點擊播放按鈕")
            print("5. 影片將在彈出視窗中播放")
            return 0
        else:
            print("\n⚠️  部分功能有問題，請檢查日誌")
            return 1
            
    finally:
        # 清理
        if server_process:
            print("\n🛑 停止測試服務器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except:
                server_process.kill()

if __name__ == "__main__":
    sys.exit(main())