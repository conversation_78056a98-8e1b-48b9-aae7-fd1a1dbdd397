#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試列表模式的Web介面
"""

import requests
import time
import threading
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8086, debug=False)

def main():
    print("=" * 60)
    print("NVRV-Free Web介面列表模式測試")
    print("=" * 60)
    
    # 啟動服務器
    print("🚀 啟動Web服務器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(2)
    
    try:
        # 1. 測試頻道載入速度
        print("\n⚡ 測試頻道載入速度...")
        start_time = time.time()
        response = requests.get('http://127.0.0.1:8086/api/channels', timeout=5)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"   ✅ 超快速載入: {len(channels)} 個頻道 ({load_time:.3f}秒)")
                
                # 顯示頻道摘要
                ready_channels = [ch for ch in channels if ch.get('status') == 'ready']
                offline_channels = [ch for ch in channels if ch.get('status') == 'offline']
                
                print(f"   📊 頻道狀態:")
                print(f"      🟡 就緒 (有RTSP): {len(ready_channels)} 個")
                print(f"      🔴 離線 (無RTSP): {len(offline_channels)} 個")
                
                # 顯示前5個就緒頻道
                if ready_channels:
                    print(f"\n   📺 就緒頻道列表:")
                    for i, ch in enumerate(ready_channels[:5]):
                        name = ch.get('name', 'Unknown')
                        save_path = ch.get('save_path', './recordings')
                        print(f"      {i+1}. {name}")
                        print(f"         儲存: {save_path}")
                    
                    if len(ready_channels) > 5:
                        print(f"      ... 還有 {len(ready_channels) - 5} 個頻道")
                
            else:
                print(f"   ❌ API錯誤: {data.get('error')}")
                return
        else:
            print(f"   ❌ HTTP錯誤: {response.status_code}")
            return
        
        # 2. 測試主頁面載入
        print(f"\n🌐 測試主頁面載入...")
        start_time = time.time()
        response = requests.get('http://127.0.0.1:8086/', timeout=5)
        load_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"   ✅ 主頁面載入成功 ({load_time:.3f}秒)")
            
            # 檢查頁面內容
            content = response.text
            if '列表模式' in content and '網格模式' in content:
                print(f"   ✅ 包含列表/網格模式切換功能")
            if '頻道列表' in content:
                print(f"   ✅ 包含頻道列表表格")
            if 'channelPreviewModal' in content:
                print(f"   ✅ 包含預覽模態框")
        else:
            print(f"   ❌ 主頁面載入失敗: {response.status_code}")
        
        # 3. 測試單一頻道預覽（不實際連線）
        if ready_channels:
            first_channel = ready_channels[0]
            channel_id = first_channel.get('id')
            channel_name = first_channel.get('name')
            
            print(f"\n📹 測試預覽端點...")
            print(f"   測試頻道: {channel_name}")
            
            try:
                # 測試串流端點是否回應
                response = requests.get(f'http://127.0.0.1:8086/api/live_stream/{channel_id}', 
                                      timeout=3, stream=True)
                
                if response.status_code == 200:
                    print(f"   ✅ 預覽端點回應正常")
                    response.close()
                else:
                    print(f"   ⚠️  預覽端點: HTTP {response.status_code}")
            except Exception as e:
                print(f"   ⚠️  預覽端點: {e} (正常，RTSP可能無法連線)")
        
        print(f"\n" + "=" * 60)
        print("✅ 列表模式測試完成 - 新功能特點:")
        print("   📋 預設顯示頻道列表，不自動載入預覽")
        print("   ⚡ 頁面載入極快，無RTSP連線阻塞")
        print("   🎯 點擊預覽按鈕才開始串流連線")
        print("   🔄 支援列表模式和網格模式切換")
        print("   📱 列表模式更適合大量頻道管理")
        print("   🖼️  預覽模態框提供詳細頻道資訊")
        print(f"\n🌐 Web介面已就緒，請訪問:")
        print(f"   http://localhost:8080")
        print(f"   - 預設為列表模式，點擊「預覽」按鈕查看即時畫面")
        print(f"   - 可切換到網格模式進行多頻道同時預覽")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == '__main__':
    main()