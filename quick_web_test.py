#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速Web介面測試
"""

import json
import threading
import time
import requests
from nvrv_web_server import NVRVWebServer

def start_test_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8081, debug=False)

def test_web_interface():
    """測試Web介面"""
    print("🚀 啟動測試服務器...")
    
    # 在背景線程中啟動服務器
    server_thread = threading.Thread(target=start_test_server, daemon=True)
    server_thread.start()
    
    # 等待服務器啟動
    time.sleep(3)
    
    print("🔍 測試Web介面...")
    
    try:
        # 測試主頁面
        response = requests.get('http://127.0.0.1:8081/', timeout=5)
        if response.status_code == 200:
            print("✅ 主頁面載入正常")
        else:
            print(f"❌ 主頁面載入失敗: {response.status_code}")
        
        # 測試頻道API
        response = requests.get('http://127.0.0.1:8081/api/channels', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"✅ 頻道API正常，返回 {len(channels)} 個頻道")
                
                # 顯示前5個頻道
                print("\n📋 頻道列表:")
                for i, channel in enumerate(channels[:5]):
                    name = channel.get('name', 'Unknown')
                    rtsp_url = channel.get('rtsp_url', '')
                    save_path = channel.get('save_path', '')
                    status = channel.get('status', 'unknown')
                    
                    print(f"   {i+1}. {name}")
                    print(f"      狀態: {status}")
                    print(f"      RTSP: {rtsp_url[:50]}..." if rtsp_url else "      RTSP: (未設定)")
                    print(f"      儲存: {save_path}")
                    print()
                
                if len(channels) > 5:
                    print(f"   ... 還有 {len(channels) - 5} 個頻道")
                
                # 統計頻道狀態
                status_count = {}
                for channel in channels:
                    status = channel.get('status', 'unknown')
                    status_count[status] = status_count.get(status, 0) + 1
                
                print("\n📊 頻道狀態統計:")
                for status, count in status_count.items():
                    status_icon = {
                        'online': '🟢',
                        'offline': '🔴',
                        'demo': '🟡',
                        'error': '⚠️',
                        'connecting': '🔵'
                    }.get(status, '❓')
                    print(f"   {status_icon} {status}: {count} 個")
                
            else:
                print(f"❌ 頻道API錯誤: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ 頻道API HTTP錯誤: {response.status_code}")
        
        # 測試歷史回放頁面
        response = requests.get('http://127.0.0.1:8081/playback', timeout=5)
        if response.status_code == 200:
            print("✅ 歷史回放頁面載入正常")
        else:
            print(f"❌ 歷史回放頁面載入失敗: {response.status_code}")
        
        print(f"\n🌐 Web介面已啟動，請在瀏覽器中訪問:")
        print(f"   http://127.0.0.1:8081")
        print(f"\n按 Ctrl+C 停止測試服務器")
        
        # 保持服務器運行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 測試服務器已停止")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 連接測試服務器失敗: {e}")
        print("請確認沒有其他程式占用端口8081")
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")

if __name__ == '__main__':
    test_web_interface()