#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows Server 編碼器測試工具
專門用於診斷乾淨系統上的編碼器問題
"""

import cv2
import numpy as np
import os
import sys
import time
from datetime import datetime

def test_codec_with_size(codec_name, codec_desc):
    """測試編碼器並檢查檔案大小"""
    print(f"\n🧪 測試 {codec_desc} ({codec_name})")
    print("-" * 40)
    
    try:
        # 測試參數
        width, height = 640, 360
        fps = 8
        duration_seconds = 10  # 10秒測試
        test_filename = f"test_{codec_name}_{int(time.time())}.mp4"
        
        # 創建VideoWriter
        fourcc = cv2.VideoWriter_fourcc(*codec_name)
        writer = cv2.VideoWriter(test_filename, fourcc, fps, (width, height))
        
        if not writer.isOpened():
            print(f"❌ VideoWriter無法開啟")
            writer.release()
            return False, 0, 0
        
        print(f"✅ VideoWriter創建成功")
        
        # 設置壓縮參數
        try:
            if 'MJPG' in codec_name:
                writer.set(cv2.VIDEOWRITER_PROP_QUALITY, 10)  # MJPG極低品質
                print(f"📉 設定MJPG品質為10")
            else:
                writer.set(cv2.VIDEOWRITER_PROP_QUALITY, 30)  # 其他編碼器低品質
                print(f"📉 設定品質為30")
        except:
            print(f"⚠️  無法設定品質參數")
        
        # 生成測試幀
        total_frames = fps * duration_seconds
        print(f"🎬 生成 {total_frames} 幀測試影片...")
        
        start_time = time.time()
        
        for i in range(total_frames):
            # 創建測試幀（彩色漸變）
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # 創建動態內容
            color_shift = int((i / total_frames) * 255)
            frame[:, :, 0] = color_shift  # 藍色通道
            frame[:, :, 1] = 128  # 綠色通道固定
            frame[:, :, 2] = 255 - color_shift  # 紅色通道
            
            # 添加文字
            cv2.putText(frame, f"Frame {i+1}/{total_frames}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Codec: {codec_name}", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 寫入幀
            writer.write(frame)
            
            # 進度顯示
            if (i + 1) % (total_frames // 4) == 0:
                progress = ((i + 1) / total_frames) * 100
                print(f"📊 進度: {progress:.0f}%")
        
        encoding_time = time.time() - start_time
        writer.release()
        
        # 檢查結果
        if os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            file_size_mb = file_size / (1024 * 1024)
            
            print(f"✅ 測試完成!")
            print(f"📁 檔案大小: {file_size_mb:.2f} MB")
            print(f"⏱️  編碼時間: {encoding_time:.2f} 秒")
            print(f"📊 每秒大小: {file_size_mb/duration_seconds:.3f} MB/秒")
            
            # 評估檔案大小
            expected_mb_per_sec = {
                'H264': 0.15, 'avc1': 0.15, 'X264': 0.18,
                'XVID': 0.25, 'mp4v': 0.30, 'MJPG': 0.80, 'DIVX': 0.35
            }
            
            expected = expected_mb_per_sec.get(codec_name, 0.5)
            actual = file_size_mb / duration_seconds
            
            if actual <= expected * 1.5:
                print(f"✅ 檔案大小正常 (預期: {expected:.2f} MB/秒)")
            else:
                print(f"⚠️  檔案偏大 (預期: {expected:.2f} MB/秒, 實際: {actual:.2f} MB/秒)")
            
            # 清理測試檔案
            os.remove(test_filename)
            
            return True, file_size_mb, encoding_time
        else:
            print(f"❌ 測試檔案未生成")
            return False, 0, 0
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False, 0, 0

def check_system_info():
    """檢查系統信息"""
    print("🖥️  系統信息")
    print("=" * 50)
    
    import platform
    print(f"作業系統: {platform.system()} {platform.release()}")
    print(f"架構: {platform.architecture()[0]}")
    print(f"處理器: {platform.processor()}")
    print(f"Python版本: {sys.version}")
    print(f"OpenCV版本: {cv2.__version__}")
    
    # 檢查重要的DLL
    opencv_path = os.path.dirname(cv2.__file__)
    print(f"OpenCV路徑: {opencv_path}")
    
    important_dlls = [
        'opencv_ffmpeg*.dll',
        'opencv_world*.dll'
    ]
    
    print("\n📚 重要DLL檢查:")
    import glob
    for dll_pattern in important_dlls:
        dll_files = glob.glob(os.path.join(opencv_path, dll_pattern))
        if dll_files:
            for dll in dll_files:
                print(f"✅ {os.path.basename(dll)}")
        else:
            print(f"❌ {dll_pattern} - 未找到")

def main():
    print("NVRV-Free Windows Server 編碼器診斷工具")
    print("=" * 60)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 系統信息檢查
    check_system_info()
    
    print("\n🧪 編碼器測試")
    print("=" * 50)
    
    # 編碼器測試順序（按NVRV-Free的優先順序）
    codecs_to_test = [
        ('H264', 'H.264編碼器 (最佳壓縮)'),
        ('avc1', 'AVC1編碼器 (H.264相容)'),
        ('X264', 'X264編碼器 (H.264實現)'),
        ('XVID', 'XVID編碼器 (高相容性)'),
        ('mp4v', 'MP4V編碼器 (標準)'),
        ('MJPG', 'MJPG編碼器 (備用)'),
        ('DIVX', 'DIVX編碼器 (最後備用)')
    ]
    
    results = []
    
    for codec_name, codec_desc in codecs_to_test:
        success, file_size, encoding_time = test_codec_with_size(codec_name, codec_desc)
        results.append((codec_name, codec_desc, success, file_size, encoding_time))
    
    # 結果摘要
    print("\n" + "=" * 60)
    print("📊 測試結果摘要")
    print("=" * 60)
    
    working_codecs = [r for r in results if r[2]]  # 成功的編碼器
    failed_codecs = [r for r in results if not r[2]]  # 失敗的編碼器
    
    if working_codecs:
        print(f"✅ 可用編碼器 ({len(working_codecs)}/{len(results)}):")
        for codec_name, codec_desc, success, file_size, encoding_time in working_codecs:
            print(f"   {codec_desc}: {file_size:.2f} MB (10秒)")
        
        # 推薦使用的編碼器
        best_codec = working_codecs[0]
        print(f"\n🎯 NVRV-Free將使用: {best_codec[1]}")
        
        # 預估20分鐘檔案大小
        estimated_20min = (best_codec[3] / 10) * 20 * 60  # 轉換為20分鐘
        print(f"📊 預估20分鐘檔案大小: {estimated_20min:.1f} MB")
        
        if estimated_20min > 100:
            print(f"⚠️  檔案可能偏大，建議安裝更好的編碼器")
    else:
        print("❌ 沒有可用的編碼器!")
    
    if failed_codecs:
        print(f"\n❌ 失敗的編碼器 ({len(failed_codecs)}):")
        for codec_name, codec_desc, success, file_size, encoding_time in failed_codecs:
            print(f"   {codec_desc}")
    
    print("\n💡 建議:")
    if any('MJPG' in r[0] for r in working_codecs[:3]):  # 如果前3個是MJPG
        print("- 安裝 Visual C++ Redistributable 2015-2022")
        print("- 安裝 K-Lite Codec Pack Basic")
        print("- 重新啟動系統")
    
    print(f"\n📄 詳細日誌已保存")
    
    input("\n按Enter鍵退出...")

if __name__ == "__main__":
    main()