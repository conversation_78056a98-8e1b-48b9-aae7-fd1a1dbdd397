#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試播放功能修復的腳本
"""

import requests
import json
import time
import threading
import subprocess
import sys
import os

def start_web_server():
    """在背景啟動 Web 服務器"""
    try:
        # 啟動 Web 服務器
        process = subprocess.Popen(
            [sys.executable, 'start_web_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服務器啟動
        time.sleep(3)
        
        return process
    except Exception as e:
        print(f"啟動 Web 服務器失敗: {e}")
        return None

def test_video_api():
    """測試影片播放 API"""
    print("=== 測試影片播放 API ===")
    
    base_url = "http://localhost:8080"
    
    try:
        # 測試頻道 API
        response = requests.get(f"{base_url}/api/channels", timeout=10)
        if response.status_code != 200:
            print(f"❌ 頻道 API 失敗: {response.status_code}")
            return False
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ 頻道 API 錯誤: {data.get('error')}")
            return False
        
        channels = data.get('channels', [])
        print(f"✅ 載入 {len(channels)} 個頻道")
        
        # 測試第一個有錄影的頻道
        for channel in channels[:5]:
            channel_id = channel.get('id')
            channel_name = channel.get('name')
            
            # 獲取錄影列表
            today = "20250731"  # 使用今天的日期
            response = requests.get(
                f"{base_url}/api/recordings?channel_id={channel_id}&date={today}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    recordings = data.get('recordings', [])
                    print(f"✅ 頻道 {channel_id} ({channel_name}): {len(recordings)} 個錄影")
                    
                    if recordings:
                        # 測試播放第一個影片
                        test_filename = recordings[0]['filename']
                        print(f"🎬 測試播放: {test_filename}")
                        
                        try:
                            # 測試 HEAD 請求
                            response = requests.head(
                                f"{base_url}/api/video/{test_filename}",
                                timeout=15
                            )
                            
                            if response.status_code == 200:
                                content_length = response.headers.get('Content-Length', '0')
                                content_type = response.headers.get('Content-Type', 'unknown')
                                print(f"✅ 影片可播放: {content_type}, {content_length} bytes")
                                
                                # 測試實際下載前幾個字節
                                response = requests.get(
                                    f"{base_url}/api/video/{test_filename}",
                                    headers={'Range': 'bytes=0-1023'},
                                    timeout=15
                                )
                                
                                if response.status_code in [200, 206]:
                                    print(f"✅ 影片串流正常: {len(response.content)} bytes received")
                                    return True
                                else:
                                    print(f"❌ 影片串流失敗: {response.status_code}")
                            else:
                                print(f"❌ 影片無法訪問: {response.status_code}")
                                if response.status_code == 500:
                                    try:
                                        error_data = response.json()
                                        print(f"   錯誤詳情: {error_data.get('error', 'Unknown error')}")
                                    except:
                                        pass
                        except Exception as e:
                            print(f"❌ 測試播放失敗: {e}")
                            continue
                else:
                    print(f"⚠️  頻道 {channel_id} 錄影 API 錯誤: {data.get('error')}")
            else:
                print(f"❌ 頻道 {channel_id} 錄影 API 失敗: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("NVRV-Free 播放功能修復測試")
    print("=" * 50)
    
    # 檢查是否已有 Web 服務器運行
    try:
        response = requests.get("http://localhost:8080/", timeout=3)
        print("✅ Web 服務器已在運行")
        server_process = None
    except:
        print("🚀 啟動 Web 服務器...")
        server_process = start_web_server()
        if not server_process:
            print("❌ 無法啟動 Web 服務器")
            return 1
        
        # 等待服務器完全啟動
        for i in range(10):
            try:
                response = requests.get("http://localhost:8080/", timeout=2)
                if response.status_code == 200:
                    print("✅ Web 服務器啟動成功")
                    break
            except:
                time.sleep(1)
                print(f"⏳ 等待服務器啟動... ({i+1}/10)")
        else:
            print("❌ Web 服務器啟動超時")
            if server_process:
                server_process.terminate()
            return 1
    
    try:
        # 測試播放功能
        success = test_video_api()
        
        if success:
            print("\n🎉 播放功能修復成功！")
            print("現在可以在 Web 介面中正常播放影片了")
            print("請訪問: http://localhost:8080/playback")
            return 0
        else:
            print("\n⚠️  播放功能仍有問題")
            print("請檢查日誌輸出以獲取更多資訊")
            return 1
            
    finally:
        # 清理
        if server_process:
            print("\n🛑 停止測試服務器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except:
                server_process.kill()

if __name__ == "__main__":
    sys.exit(main())