#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試解析度修復的腳本
檢查 force_640x360 設定是否正確工作
"""

import json
import sys
import os

def test_settings_file():
    """測試設定檔中的 force_640x360 設定"""
    print("=== 測試設定檔 ===")
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        print(f"總共有 {len(settings)} 個頻道設定")
        
        force_640_count = 0
        original_res_count = 0
        
        for i, channel in enumerate(settings):
            channel_name = channel.get('channel_name', f'Channel {i+1}')
            force_640 = channel.get('force_640x360', True)  # 預設為 True
            rtsp_url = channel.get('rtsp_url', '')
            
            if force_640:
                force_640_count += 1
                status = "強制640x360"
            else:
                original_res_count += 1
                status = "使用原始解析度"
            
            # 只顯示有 RTSP URL 的頻道
            if rtsp_url:
                print(f"頻道 {i+1:2d}: {channel_name:<30} - {status}")
        
        print(f"\n統計:")
        print(f"- 使用原始解析度: {original_res_count} 個頻道")
        print(f"- 強制640x360:    {force_640_count} 個頻道")
        
        return True
        
    except FileNotFoundError:
        print("錯誤: 找不到 settings_free.json 檔案")
        return False
    except json.JSONDecodeError as e:
        print(f"錯誤: 設定檔格式錯誤 - {e}")
        return False
    except Exception as e:
        print(f"錯誤: {e}")
        return False

def check_code_fix():
    """檢查代碼修復是否正確"""
    print("\n=== 檢查代碼修復 ===")
    
    try:
        with open('NVRV-free.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否包含修復的代碼
        if "if self.force_640x360.get():" in content:
            print("✅ 代碼已修復: 包含 force_640x360 檢查")
        else:
            print("❌ 代碼未修復: 缺少 force_640x360 檢查")
            return False
        
        # 檢查是否移除了硬編碼
        if "# 強制使用640x360解析度以節省儲存空間\n                width = 640\n                height = 360" in content:
            print("❌ 代碼仍有問題: 包含硬編碼的640x360")
            return False
        else:
            print("✅ 代碼修復正確: 已移除硬編碼的640x360")
        
        # 檢查時間戳位置是否動態調整
        if "timestamp_y = height - 10" in content:
            print("✅ 時間戳位置已修復: 支援動態調整")
        else:
            print("❌ 時間戳位置未修復: 可能仍使用固定位置")
            return False
        
        return True
        
    except FileNotFoundError:
        print("錯誤: 找不到 NVRV-free.py 檔案")
        return False
    except Exception as e:
        print(f"錯誤: {e}")
        return False

def main():
    """主函數"""
    print("NVRV-Free 解析度修復測試")
    print("=" * 50)
    
    # 測試設定檔
    settings_ok = test_settings_file()
    
    # 檢查代碼修復
    code_ok = check_code_fix()
    
    print("\n=== 測試結果 ===")
    if settings_ok and code_ok:
        print("✅ 所有測試通過!")
        print("\n修復說明:")
        print("1. 當 force_640x360 設為 false 時，將使用原始解析度錄影")
        print("2. 當 force_640x360 設為 true 時，將強制轉換為 640x360")
        print("3. 時間戳位置會根據解析度自動調整")
        print("\n使用方法:")
        print("- 在程式的設定區域中，取消勾選「強制640x360 (節省空間)」")
        print("- 或在 settings_free.json 中將 force_640x360 設為 false")
        return 0
    else:
        print("❌ 測試失敗，請檢查修復")
        return 1

if __name__ == "__main__":
    sys.exit(main())