#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試歷史回放按鈕功能
"""

import requests
import time
import threading
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8088, debug=False)

def main():
    print("=" * 60)
    print("NVRV-Free 歷史回放按鈕測試")
    print("=" * 60)
    
    # 啟動服務器
    print("🚀 啟動Web服務器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(2)
    
    try:
        # 1. 測試主頁面載入
        print("\n🌐 測試主頁面...")
        response = requests.get('http://127.0.0.1:8088/', timeout=5)
        
        if response.status_code == 200:
            content = response.text
            print("✅ 主頁面載入成功")
            
            # 檢查JavaScript函數是否存在
            if 'function goToPlayback(' in content:
                print("✅ goToPlayback函數存在")
            else:
                print("❌ goToPlayback函數不存在")
            
            if 'function goToChannelPlayback(' in content:
                print("✅ goToChannelPlayback函數存在")
            else:
                print("❌ goToChannelPlayback函數不存在")
            
            # 檢查按鈕HTML是否正確
            if 'onclick="goToPlayback(' in content:
                print("✅ 列表模式歷史回放按鈕存在")
            else:
                print("❌ 列表模式歷史回放按鈕不存在")
            
            if 'onclick="goToChannelPlayback(' in content:
                print("✅ 預覽模式歷史回放按鈕存在")
            else:
                print("❌ 預覽模式歷史回放按鈕不存在")
                
        else:
            print(f"❌ 主頁面載入失敗: {response.status_code}")
            return
        
        # 2. 測試歷史回放頁面
        print(f"\n📹 測試歷史回放頁面...")
        response = requests.get('http://127.0.0.1:8088/playback', timeout=5)
        
        if response.status_code == 200:
            print("✅ 歷史回放頁面載入成功")
            
            # 檢查頁面內容
            content = response.text
            if 'channelSelect' in content:
                print("✅ 包含頻道選擇器")
            if 'dateSelect' in content:
                print("✅ 包含日期選擇器")
            if 'playVideo' in content:
                print("✅ 包含播放功能")
                
        else:
            print(f"❌ 歷史回放頁面載入失敗: {response.status_code}")
        
        # 3. 測試帶參數的歷史回放頁面
        print(f"\n🎯 測試帶頻道參數的歷史回放頁面...")
        response = requests.get('http://127.0.0.1:8088/playback?channel=0', timeout=5)
        
        if response.status_code == 200:
            print("✅ 帶參數的歷史回放頁面載入成功")
            
            content = response.text
            # 檢查是否包含處理URL參數的JavaScript
            if 'urlParams.get(\'channel\')' in content:
                print("✅ 包含URL參數處理邏輯")
            else:
                print("❌ 缺少URL參數處理邏輯")
                
        else:
            print(f"❌ 帶參數的歷史回放頁面載入失敗: {response.status_code}")
        
        # 4. 檢查JavaScript控制台錯誤
        print(f"\n🔍 JavaScript函數測試建議:")
        print(f"   1. 在瀏覽器中打開: http://localhost:8080")
        print(f"   2. 按F12打開開發者工具")
        print(f"   3. 在控制台中測試:")
        print(f"      - goToPlayback(0)  // 測試跳轉到頻道0的歷史回放")
        print(f"      - goToChannelPlayback()  // 測試預覽模式的歷史回放")
        print(f"   4. 檢查是否有JavaScript錯誤")
        
        print(f"\n" + "=" * 60)
        print("✅ 歷史回放按鈕測試完成")
        print(f"\n📋 可能的問題和解決方案:")
        print(f"   1. JavaScript錯誤 - 檢查瀏覽器控制台")
        print(f"   2. 按鈕事件未綁定 - 確認onclick屬性正確")
        print(f"   3. 函數作用域問題 - 確認函數在全局作用域")
        print(f"   4. 頁面載入順序 - 確認DOM載入完成後再綁定事件")
        
        print(f"\n🌐 測試地址:")
        print(f"   主頁面: http://localhost:8080")
        print(f"   歷史回放: http://localhost:8080/playback")
        print(f"   帶參數: http://localhost:8080/playback?channel=0")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == '__main__':
    main()