#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復串流顯示問題
"""

import json

def main():
    print("修復串流顯示問題...")
    
    # 創建可用的演示設定
    working_settings = [
        {
            "channel_name": "演示頻道1",
            "rtsp_url": "demo://channel1",  # 使用demo前綴
            "save_path": "./recordings"
        },
        {
            "channel_name": "演示頻道2",
            "rtsp_url": "demo://channel2",
            "save_path": "./recordings"
        }
    ]
    
    # 備份並替換設定檔
    try:
        with open('settings_free.json.backup', 'w', encoding='utf-8') as f:
            with open('settings_free.json', 'r', encoding='utf-8') as original:
                f.write(original.read())
        print("原設定檔已備份")
    except:
        pass
    
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(working_settings, f, ensure_ascii=False, indent=2)
    
    print("設定檔已更新為演示模式")
    print("現在重新啟動Web介面，應該能看到演示畫面")
    print()
    print("步驟:")
    print("1. 停止當前的Web服務器 (按Ctrl+C)")
    print("2. 重新執行: python simple_web.py")
    print("3. 瀏覽器訪問: http://localhost:8080")

if __name__ == '__main__':
    main()