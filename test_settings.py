#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試設定檔讀取
"""

import json
import os
import requests
import time

def test_settings_file():
    """測試設定檔"""
    print("🔍 測試設定檔讀取...")
    
    if not os.path.exists('settings_free.json'):
        print("❌ settings_free.json 不存在")
        return False
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        print(f"✅ 設定檔讀取成功，找到 {len(settings)} 個頻道:")
        for i, channel in enumerate(settings):
            name = channel.get('channel_name', 'Unknown')
            url = channel.get('rtsp_url', 'No URL')
            print(f"   頻道 {i}: {name} - {url}")
        
        return True
    except Exception as e:
        print(f"❌ 設定檔讀取失敗: {e}")
        return False

def test_api_channels():
    """測試API頻道讀取"""
    print("\n🔍 測試API頻道讀取...")
    
    try:
        response = requests.get('http://localhost:8080/api/channels', timeout=5)
        data = response.json()
        
        if data.get('success'):
            channels = data.get('channels', [])
            print(f"✅ API返回 {len(channels)} 個頻道:")
            for channel in channels:
                print(f"   ID {channel['id']}: {channel['name']} - {channel['rtsp_url']}")
            return True
        else:
            print(f"❌ API錯誤: {data.get('error')}")
            return False
    except Exception as e:
        print(f"❌ API請求失敗: {e}")
        return False

def main():
    print("🧪 設定檔和API測試")
    print("=" * 50)
    
    # 測試設定檔
    settings_ok = test_settings_file()
    
    # 測試API（需要Web服務器運行）
    print("等待Web服務器啟動...")
    time.sleep(2)
    
    api_ok = test_api_channels()
    
    print("\n📊 測試結果:")
    print("=" * 50)
    print(f"設定檔讀取: {'✅ 正常' if settings_ok else '❌ 異常'}")
    print(f"API頻道讀取: {'✅ 正常' if api_ok else '❌ 異常'}")
    
    if settings_ok and not api_ok:
        print("\n💡 建議:")
        print("1. 確保Web服務器正在運行")
        print("2. 檢查埠號是否正確 (8080)")
        print("3. 查看Web服務器日誌")

if __name__ == '__main__':
    main()