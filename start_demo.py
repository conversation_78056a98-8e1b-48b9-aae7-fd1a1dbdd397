#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
啟動演示模式Web介面
"""

import os
import json
import subprocess
import sys

def setup_demo():
    """設置演示環境"""
    print("🎬 設置演示環境...")
    print("=" * 50)
    
    # 創建演示設定
    demo_settings = [
        {
            "channel_name": "演示頻道1",
            "rtsp_url": "demo://channel1",
            "save_path": "./recordings"
        },
        {
            "channel_name": "演示頻道2", 
            "rtsp_url": "demo://channel2",
            "save_path": "./recordings"
        },
        {
            "channel_name": "演示頻道3",
            "rtsp_url": "demo://channel3",
            "save_path": "./recordings"
        }
    ]
    
    # 備份原設定檔
    if os.path.exists('settings_free.json'):
        os.rename('settings_free.json', 'settings_free.json.backup')
        print("✅ 原設定檔已備份")
    
    # 創建演示設定檔
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(demo_settings, f, ensure_ascii=False, indent=2)
    
    # 創建目錄
    os.makedirs('./recordings', exist_ok=True)
    os.makedirs('./static', exist_ok=True)
    
    print("✅ 演示設定已創建")

def create_demo_recordings():
    """創建演示錄影檔案"""
    print("🎥 創建演示錄影檔案...")
    
    try:
        result = subprocess.run([sys.executable, 'create_demo_stream.py'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ 演示錄影檔案已創建")
        else:
            print(f"⚠️  創建錄影檔案時出現警告: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⚠️  創建錄影檔案超時，跳過此步驟")
    except Exception as e:
        print(f"⚠️  無法創建錄影檔案: {e}")

def main():
    print("🚀 NVRV-Free 演示模式")
    print("=" * 50)
    print("此模式將顯示模擬的串流畫面，無需真實的RTSP源")
    print()
    
    # 設置演示環境
    setup_demo()
    
    # 創建演示錄影檔案
    create_demo_recordings()
    
    print("\n🌐 啟動Web介面...")
    print("=" * 50)
    print("網址: http://localhost:8081")
    print("功能:")
    print("  ✅ 3個演示頻道 (動態畫面)")
    print("  ✅ 歷史記錄瀏覽")
    print("  ✅ 串流連線測試")
    print("  ✅ 完整Web介面")
    print()
    print("按 Ctrl+C 停止服務")
    print("=" * 50)
    
    try:
        # 啟動Web介面
        from web_interface import NVRVWebInterface
        web_interface = NVRVWebInterface()
        web_interface.run(host='127.0.0.1', port=8081, debug=False)
        
    except KeyboardInterrupt:
        print("\n⏹️  演示會話結束")
        
        # 恢復原設定檔
        if os.path.exists('settings_free.json.backup'):
            os.rename('settings_free.json.backup', 'settings_free.json')
            print("✅ 原設定檔已恢復")
            
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()