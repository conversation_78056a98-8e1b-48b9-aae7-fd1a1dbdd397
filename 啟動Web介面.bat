@echo off
echo 正在啟動NVRV-Free Web介面...
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 未找到Python，請先安裝Python 3.7+
    pause
    exit /b 1
)

REM 安裝依賴套件
echo 正在檢查依賴套件...
pip install -r web_requirements.txt

@echo off
echo 🚀 NVRV-Free Web介面啟動
echo ================================

REM 檢查依賴套件
echo 正在檢查依賴套件...
python check_dependencies.py
if errorlevel 1 (
    echo.
    echo 請先安裝缺少的套件
    pause
    exit /b 1
)

echo.
echo 選擇啟動模式:
echo 1. 演示模式 (推薦) - 使用模擬畫面
echo 2. 實際模式 - 使用真實RTSP串流
echo.
set /p choice="請選擇 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 啟動演示模式...
    echo 網址: http://localhost:8080
    echo 功能: 3個演示頻道 + 完整Web介面
    echo 按 Ctrl+C 停止服務
    echo.
    python start_demo.py
) else if "%choice%"=="2" (
    echo.
    echo 啟動實際模式...
    echo 網址: http://localhost:8080
    echo 功能: 真實RTSP串流監控
    echo 按 Ctrl+C 停止服務
    echo.
    python simple_web.py
) else (
    echo 無效選擇，使用演示模式
    python start_demo.py
)

pause

pause