#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試配置文件讀取
"""

import json
import os
from nvrv_web_server import NVRVWebServer

def test_config_loading():
    """測試配置文件讀取"""
    print("=" * 50)
    print("測試 settings_free.json 配置讀取")
    print("=" * 50)
    
    # 1. 檢查配置文件是否存在
    if not os.path.exists('settings_free.json'):
        print("❌ settings_free.json 文件不存在")
        return False
    
    print("✅ settings_free.json 文件存在")
    
    # 2. 直接讀取配置文件
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        print(f"✅ 成功讀取配置文件，包含 {len(config_data)} 個頻道")
    except Exception as e:
        print(f"❌ 讀取配置文件失敗: {e}")
        return False
    
    # 3. 顯示前幾個頻道的詳細信息
    print("\n📋 頻道配置詳情:")
    for i, channel in enumerate(config_data[:5]):  # 只顯示前5個
        rtsp_url = channel.get('rtsp_url', '')
        channel_name = channel.get('channel_name', f'Channel {i+1}')
        save_path = channel.get('save_path', './recordings')
        
        print(f"   頻道 {i+1}: {channel_name}")
        print(f"      RTSP URL: {rtsp_url if rtsp_url else '(未設定)'}")
        print(f"      儲存路徑: {save_path}")
        print(f"      錄影時長: {channel.get('record_duration', 30)} 分鐘")
        print(f"      保存天數: {channel.get('keep_days', 7)} 天")
        print()
    
    if len(config_data) > 5:
        print(f"   ... 還有 {len(config_data) - 5} 個頻道")
    
    # 4. 測試Web服務器的配置讀取
    print("\n🌐 測試Web服務器配置讀取:")
    try:
        web_server = NVRVWebServer()
        loaded_channels = web_server.channels_config
        
        if len(loaded_channels) == len(config_data):
            print(f"✅ Web服務器成功載入 {len(loaded_channels)} 個頻道")
        else:
            print(f"⚠️  Web服務器載入頻道數量不符: 預期 {len(config_data)}, 實際 {len(loaded_channels)}")
        
        # 檢查前幾個頻道的詳細信息
        print("\n📋 Web服務器載入的頻道:")
        for i, channel in enumerate(loaded_channels[:5]):
            rtsp_url = channel.get('rtsp_url', '')
            channel_name = channel.get('channel_name', f'Channel {i+1}')
            save_path = channel.get('save_path', './recordings')
            
            print(f"   頻道 {i+1}: {channel_name}")
            print(f"      RTSP URL: {rtsp_url if rtsp_url else '(未設定)'}")
            print(f"      儲存路徑: {save_path}")
            print()
        
    except Exception as e:
        print(f"❌ Web服務器配置讀取失敗: {e}")
        return False
    
    # 5. 統計頻道類型
    rtsp_channels = sum(1 for ch in config_data if ch.get('rtsp_url', ''))
    empty_channels = sum(1 for ch in config_data if not ch.get('rtsp_url', ''))
    demo_channels = sum(1 for ch in config_data if ch.get('rtsp_url', '').startswith('demo://'))
    
    print("\n📊 頻道統計:")
    print(f"   總頻道數: {len(config_data)}")
    print(f"   有RTSP URL的頻道: {rtsp_channels}")
    print(f"   空白頻道: {empty_channels}")
    print(f"   演示頻道: {demo_channels}")
    
    # 6. 檢查儲存路徑
    print("\n📁 儲存路徑檢查:")
    unique_paths = set()
    for channel in config_data:
        save_path = channel.get('save_path', './recordings')
        unique_paths.add(save_path)
    
    print(f"   使用的儲存路徑數量: {len(unique_paths)}")
    for path in sorted(unique_paths):
        exists = "✅" if os.path.exists(path) else "❌"
        print(f"   {exists} {path}")
    
    print("\n" + "=" * 50)
    print("配置讀取測試完成")
    print("=" * 50)
    
    return True

def test_api_response():
    """測試API回應"""
    print("\n🔍 測試API回應:")
    
    try:
        web_server = NVRVWebServer()
        
        # 模擬API調用
        with web_server.app.test_client() as client:
            response = client.get('/api/channels')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    channels = data.get('channels', [])
                    print(f"✅ API回應正常，返回 {len(channels)} 個頻道")
                    
                    # 顯示API返回的頻道信息
                    for i, channel in enumerate(channels[:3]):
                        print(f"   API頻道 {i+1}: {channel.get('name', 'Unknown')}")
                        print(f"      狀態: {channel.get('status', 'unknown')}")
                        print(f"      RTSP: {channel.get('rtsp_url', '(無)')[:50]}...")
                        print(f"      儲存: {channel.get('save_path', '(無)')}")
                        print()
                else:
                    print(f"❌ API回應錯誤: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ API HTTP錯誤: {response.status_code}")
                
    except Exception as e:
        print(f"❌ API測試失敗: {e}")

if __name__ == '__main__':
    test_config_loading()
    test_api_response()