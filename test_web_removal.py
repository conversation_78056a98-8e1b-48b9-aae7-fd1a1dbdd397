#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 NVRV-free.py 中 Web 服務移除的腳本
確認所有 Web 相關代碼已被移除
"""

import re
import sys

def test_web_removal():
    """測試 Web 服務是否已完全移除"""
    print("=== 測試 NVRV-free.py Web 服務移除 ===")
    
    try:
        with open('NVRV-free.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查項目列表
        checks = [
            {
                'name': 'Flask 導入',
                'pattern': r'from flask import|import flask',
                'should_exist': False
            },
            {
                'name': 'SocketIO 導入',
                'pattern': r'from flask_socketio import|import flask_socketio',
                'should_exist': False
            },
            {
                'name': 'WEB_INTERFACE_AVAILABLE 變數',
                'pattern': r'WEB_INTERFACE_AVAILABLE',
                'should_exist': False
            },
            {
                'name': 'webbrowser 導入',
                'pattern': r'import webbrowser',
                'should_exist': False
            },
            {
                'name': 'Web 介面變數初始化',
                'pattern': r'self\.web_interface\s*=\s*None',
                'should_exist': False
            },
            {
                'name': 'Web 埠號變數',
                'pattern': r'self\.web_port',
                'should_exist': False
            },
            {
                'name': 'Web 狀態回調',
                'pattern': r'web_status_callback',
                'should_exist': False
            },
            {
                'name': 'Web 介面啟動方法',
                'pattern': r'def start_web_interface',
                'should_exist': False
            },
            {
                'name': 'Web 介面停止方法',
                'pattern': r'def stop_web_interface',
                'should_exist': False
            },
            {
                'name': 'Web 狀態通知方法',
                'pattern': r'def notify_web_status_change',
                'should_exist': False
            },
            {
                'name': 'Web 狀態設定方法',
                'pattern': r'def set_web_status_callback',
                'should_exist': False
            },
            {
                'name': 'Web 錄影控制方法',
                'pattern': r'def web_start_recording|def web_stop_recording',
                'should_exist': False
            },
            {
                'name': 'Web 按鈕',
                'pattern': r'web_start_button|web_stop_button',
                'should_exist': False
            },
            {
                'name': 'Web 介面文字',
                'pattern': r'Web介面|啟動Web|停止Web',
                'should_exist': False
            },
            {
                'name': '網站連結功能',
                'pattern': r'webbrowser\.open|訪問官方網站',
                'should_exist': False
            }
        ]
        
        results = []
        all_passed = True
        
        for check in checks:
            matches = re.findall(check['pattern'], content, re.IGNORECASE)
            found = len(matches) > 0
            
            if check['should_exist']:
                passed = found
                status = "✅ 通過" if passed else "❌ 失敗"
                detail = f"找到 {len(matches)} 個匹配" if found else "未找到"
            else:
                passed = not found
                status = "✅ 通過" if passed else "❌ 失敗"
                detail = "已移除" if not found else f"仍存在 {len(matches)} 個匹配"
                if not passed:
                    print(f"  發現殘留: {matches[:3]}...")  # 顯示前3個匹配
            
            results.append({
                'name': check['name'],
                'passed': passed,
                'status': status,
                'detail': detail
            })
            
            if not passed:
                all_passed = False
        
        # 顯示結果
        print(f"\n檢查項目: {len(checks)} 個")
        print("-" * 60)
        
        for result in results:
            print(f"{result['status']} {result['name']:<25} - {result['detail']}")
        
        print("-" * 60)
        
        if all_passed:
            print("🎉 所有檢查通過！Web 服務已完全移除")
            print("\n✨ 現在 NVRV-free.py 專注於核心錄影功能")
            print("📝 Web 服務請使用 start_web_server.py 獨立啟動")
            return True
        else:
            failed_count = sum(1 for r in results if not r['passed'])
            print(f"❌ {failed_count} 個檢查失敗，需要進一步清理")
            return False
            
    except FileNotFoundError:
        print("❌ 錯誤: 找不到 NVRV-free.py 檔案")
        return False
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def check_start_web_server():
    """檢查 start_web_server.py 是否存在"""
    print("\n=== 檢查獨立 Web 服務 ===")
    
    try:
        with open('start_web_server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵功能
        features = [
            ('Flask 應用', r'Flask\(__name__\)'),
            ('RTSP 串流處理', r'rtsp://|RTSP'),
            ('Web 路由', r'@app\.route'),
            ('模板渲染', r'render_template'),
            ('靜態檔案服務', r'static|templates')
        ]
        
        print("start_web_server.py 功能檢查:")
        for name, pattern in features:
            matches = re.findall(pattern, content, re.IGNORECASE)
            status = "✅" if matches else "❓"
            print(f"{status} {name}: {'找到' if matches else '未找到'}")
        
        print("\n✅ start_web_server.py 存在，可獨立提供 Web 服務")
        return True
        
    except FileNotFoundError:
        print("❓ start_web_server.py 不存在")
        print("💡 建議: 確保有獨立的 Web 服務檔案")
        return False
    except Exception as e:
        print(f"❌ 檢查 start_web_server.py 時發生錯誤: {e}")
        return False

def main():
    """主函數"""
    print("NVRV-Free Web 服務移除驗證")
    print("=" * 50)
    
    # 測試 Web 服務移除
    web_removed = test_web_removal()
    
    # 檢查獨立 Web 服務
    web_server_exists = check_start_web_server()
    
    print("\n" + "=" * 50)
    print("📋 總結:")
    
    if web_removed:
        print("✅ NVRV-free.py 已成功移除 Web 服務")
        print("   - 程式更加輕量化")
        print("   - 專注於核心錄影功能")
        print("   - 減少依賴套件")
    else:
        print("❌ NVRV-free.py 仍有 Web 服務殘留")
    
    if web_server_exists:
        print("✅ start_web_server.py 可提供獨立 Web 服務")
        print("   - 使用方法: python start_web_server.py")
        print("   - 功能完整且獨立")
    else:
        print("❓ 缺少獨立的 Web 服務檔案")
    
    print("\n🎯 建議:")
    print("1. 使用 NVRV-free.py 進行錄影")
    print("2. 需要 Web 介面時執行 start_web_server.py")
    print("3. 兩個程式可以同時運行")
    
    return 0 if web_removed else 1

if __name__ == "__main__":
    sys.exit(main())