#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證頻道是否正確載入
"""

import json
import os
from nvrv_web_server import NVRVWebServer

def main():
    print("=" * 60)
    print("驗證Web介面頻道載入")
    print("=" * 60)
    
    # 1. 直接讀取配置文件
    print("📋 讀取settings_free.json...")
    with open('settings_free.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"✅ 配置文件包含 {len(config)} 個頻道")
    
    # 2. 創建Web服務器實例
    print("\n🌐 創建Web服務器實例...")
    web_server = NVRVWebServer()
    
    print(f"✅ Web服務器載入 {len(web_server.channels_config)} 個頻道")
    
    # 3. 模擬API調用
    print(f"\n🔍 模擬API調用...")
    with web_server.app.test_client() as client:
        response = client.get('/api/channels')
        data = response.get_json()
        
        if data.get('success'):
            api_channels = data.get('channels', [])
            print(f"✅ API返回 {len(api_channels)} 個頻道")
            
            # 4. 顯示所有頻道
            print(f"\n📺 完整頻道列表:")
            print("-" * 80)
            
            for i, channel in enumerate(api_channels):
                name = channel.get('name', f'Channel {i+1}')
                rtsp_url = channel.get('rtsp_url', '')
                save_path = channel.get('save_path', './recordings')
                status = channel.get('status', 'unknown')
                
                # 狀態圖標
                status_icons = {
                    'online': '🟢',
                    'offline': '🔴', 
                    'demo': '🟡',
                    'error': '⚠️',
                    'connecting': '🔵'
                }
                status_icon = status_icons.get(status, '❓')
                
                print(f"{i+1:2d}. {status_icon} {name}")
                
                if rtsp_url:
                    # 顯示RTSP URL（隱藏密碼）
                    display_url = rtsp_url
                    if '@' in rtsp_url:
                        parts = rtsp_url.split('@')
                        if len(parts) == 2:
                            protocol_user = parts[0].split('//')
                            if len(protocol_user) == 2:
                                protocol = protocol_user[0] + '//'
                                user_pass = protocol_user[1]
                                if ':' in user_pass:
                                    user = user_pass.split(':')[0]
                                    display_url = f"{protocol}{user}:***@{parts[1]}"
                    
                    print(f"    RTSP: {display_url}")
                else:
                    print(f"    RTSP: (未設定)")
                
                print(f"    儲存: {save_path}")
                print(f"    狀態: {status}")
                print()
            
            # 5. 統計信息
            rtsp_channels = [ch for ch in api_channels if ch.get('rtsp_url', '')]
            empty_channels = [ch for ch in api_channels if not ch.get('rtsp_url', '')]
            
            print("=" * 60)
            print("📊 頻道統計:")
            print(f"   總頻道數: {len(api_channels)}")
            print(f"   有RTSP URL的頻道: {len(rtsp_channels)}")
            print(f"   空白頻道: {len(empty_channels)}")
            
            # 儲存路徑統計
            save_paths = set(ch.get('save_path', './recordings') for ch in api_channels)
            print(f"   使用的儲存路徑: {len(save_paths)} 個")
            
            for path in sorted(save_paths):
                exists = "✅" if os.path.exists(path) else "❌"
                print(f"      {exists} {path}")
            
            print("\n✅ 確認: Web介面已正確載入settings_free.json中的所有頻道配置")
            print("🚀 現在可以啟動Web服務器: python start_web_server.py")
            
        else:
            print(f"❌ API錯誤: {data.get('error')}")

if __name__ == '__main__':
    main()