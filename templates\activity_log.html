{% extends "base.html" %}

{% block title %}活動日誌 - NVRV-Free{% endblock %}

{% block page_title %}活動日誌{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">活動日誌篩選</h6>
    </div>
    <div class="card-body">
        <form id="filter-form" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="date-filter" class="form-label">日期</label>
                <input type="date" class="form-control" id="date-filter">
            </div>
            <div class="col-md-4">
                <label for="user-filter" class="form-label">使用者名稱</label>
                <input type="text" class="form-control" id="user-filter" placeholder="輸入使用者名稱篩選...">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">篩選</button>
                <button type="button" class="btn btn-secondary" onclick="clearFilters()">清除</button>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">活動紀錄 (最多顯示200筆)</h6>
    </div>
    <div class="card-body">
        <pre id="log-content" style="white-space: pre-wrap; word-wrap: break-word; max-height: 60vh; overflow-y: auto;">載入中...</pre>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>async function loadLog() {    const dateFilter = document.getElementById('date-filter').value;    const userFilter = document.getElementById('user-filter').value;    const logContent = document.getElementById('log-content');    logContent.textContent = '載入中...';    let url = '/api/activity_log?';    if (dateFilter) {        url += `date=${dateFilter}`;    }    if (userFilter) {        if (dateFilter) url += '&';        url += `username=${userFilter}`;    }    const response = await fetch(url);    const data = await response.json();        if (data.success) {        if (data.log.length > 0) {            logContent.textContent = data.log.join('\n');        } else {            logContent.textContent = '沒有找到符合條件的紀錄。';        }    } else {        logContent.textContent = `載入日誌失敗: ${data.error}`;    }}document.getElementById('filter-form').addEventListener('submit', function(e) {    e.preventDefault();    loadLog();});function clearFilters() {    document.getElementById('date-filter').value = '';    document.getElementById('user-filter').value = '';    loadLog();}document.addEventListener('DOMContentLoaded', loadLog);</script>
{% endblock %}