#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單檢查 dist 目錄和 EXE 文件
"""

import os
import subprocess

def check_files():
    print("=== File Check ===")
    
    # 檢查 dist 目錄
    if os.path.exists('dist'):
        print("✅ dist directory exists")
        files = os.listdir('dist')
        print(f"Files in dist: {files}")
        
        # 尋找 EXE 文件
        exe_files = [f for f in files if f.endswith('.exe')]
        if exe_files:
            print(f"✅ Found EXE files: {exe_files}")
            return exe_files[0]  # 返回第一個 EXE 文件名
        else:
            print("❌ No EXE files found in dist")
    else:
        print("❌ dist directory does not exist")
    
    return None

def test_exe(exe_name):
    print(f"\n=== Testing {exe_name} ===")
    
    exe_path = os.path.join('dist', exe_name)
    if not os.path.exists(exe_path):
        print(f"❌ {exe_path} does not exist")
        return
    
    print(f"✅ {exe_path} exists")
    print(f"File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
    
    # 嘗試運行 EXE (只運行 5 秒)
    print("\n🚀 Attempting to run EXE...")
    print("Will timeout after 5 seconds if no output...")
    
    try:
        # 切換到 dist 目錄並運行
        os.chdir('dist')
        result = subprocess.run([exe_name, '--debug'], 
                              capture_output=True, 
                              text=True, 
                              timeout=5,
                              encoding='utf-8',
                              errors='ignore')
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"STDERR:\n{result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Process timed out (this might be normal if server started)")
    except Exception as e:
        print(f"❌ Error running EXE: {e}")
    finally:
        os.chdir('..')

if __name__ == '__main__':
    exe_name = check_files()
    if exe_name:
        test_exe(exe_name)
    else:
        print("\n💡 Suggestion: Run 'immediate_fix.bat' to rebuild EXE")