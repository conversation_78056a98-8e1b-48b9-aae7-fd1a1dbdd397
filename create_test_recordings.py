#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建測試錄影檔案
用於測試歷史回放功能
"""

import os
import cv2
import numpy as np
from datetime import datetime, timedelta
import json

def create_test_video(output_path, channel_name, duration_seconds=30):
    """創建測試影片檔案"""
    
    # 確保目錄存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 影片參數
    width, height = 640, 480
    fps = 8
    total_frames = duration_seconds * fps
    
    # 創建VideoWriter
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not out.isOpened():
        print(f"❌ 無法創建影片檔案: {output_path}")
        return False
    
    print(f"🎬 創建測試影片: {output_path}")
    print(f"   解析度: {width}x{height}")
    print(f"   幀率: {fps} fps")
    print(f"   時長: {duration_seconds} 秒")
    
    # 生成測試幀
    for frame_num in range(total_frames):
        # 創建彩色背景
        hue = (frame_num * 2) % 180
        img = np.full((height, width, 3), (hue, 255, 255), dtype=np.uint8)
        img = cv2.cvtColor(img, cv2.COLOR_HSV2BGR)
        
        # 添加頻道名稱
        cv2.putText(img, channel_name, (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        
        # 添加時間戳
        timestamp = datetime.now() - timedelta(seconds=total_frames-frame_num)
        time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        cv2.putText(img, time_str, (20, height-30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 添加幀數
        cv2.putText(img, f"Frame: {frame_num+1}/{total_frames}", (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # 添加動態元素
        center_x = width // 2 + int(100 * np.sin(frame_num * 0.1))
        center_y = height // 2 + int(50 * np.cos(frame_num * 0.1))
        cv2.circle(img, (center_x, center_y), 30, (0, 255, 0), -1)
        
        # 添加測試標記
        cv2.putText(img, "TEST RECORDING", (width-250, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        out.write(img)
        
        # 顯示進度
        if (frame_num + 1) % (fps * 5) == 0:  # 每5秒顯示一次進度
            progress = (frame_num + 1) / total_frames * 100
            print(f"   進度: {progress:.1f}%")
    
    out.release()
    
    # 檢查檔案是否成功創建
    if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"✅ 測試影片創建成功")
        print(f"   檔案大小: {file_size:.2f} MB")
        return True
    else:
        print(f"❌ 測試影片創建失敗")
        return False

def create_test_recordings_for_channels():
    """為配置中的頻道創建測試錄影檔案"""
    
    print("=" * 50)
    print("創建測試錄影檔案")
    print("=" * 50)
    
    # 讀取頻道配置
    if not os.path.exists('settings_free.json'):
        print("❌ 找不到 settings_free.json 配置文件")
        return
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            channels_config = json.load(f)
    except Exception as e:
        print(f"❌ 讀取配置文件失敗: {e}")
        return
    
    print(f"📋 找到 {len(channels_config)} 個頻道配置")
    
    # 為前5個有RTSP URL的頻道創建測試錄影
    created_count = 0
    today = datetime.now().strftime('%Y%m%d')
    
    for i, channel_data in enumerate(channels_config):
        if created_count >= 5:  # 只創建前5個
            break
            
        rtsp_url = channel_data.get('rtsp_url', '')
        if not rtsp_url:  # 跳過沒有RTSP URL的頻道
            continue
            
        channel_name = channel_data.get('channel_name', f'Channel {i+1}')
        save_path = channel_data.get('save_path', './recordings')
        
        print(f"\n📹 處理頻道 {i+1}: {channel_name}")
        print(f"   儲存路徑: {save_path}")
        
        # 創建日期目錄
        date_dir = os.path.join(save_path, today)
        os.makedirs(date_dir, exist_ok=True)
        
        # 創建多個測試錄影檔案（模擬不同時間的錄影）
        for hour in [9, 12, 15, 18]:  # 模擬9點、12點、15點、18點的錄影
            timestamp = datetime.now().replace(hour=hour, minute=0, second=0)
            filename = f"{channel_name}_{timestamp.strftime('%Y%m%d_%H%M%S')}.mp4"
            output_path = os.path.join(date_dir, filename)
            
            # 如果檔案已存在，跳過
            if os.path.exists(output_path):
                print(f"   ⏭️  跳過已存在的檔案: {filename}")
                continue
            
            # 創建測試影片（30秒）
            if create_test_video(output_path, channel_name, 30):
                created_count += 1
                print(f"   ✅ 創建: {filename}")
            else:
                print(f"   ❌ 創建失敗: {filename}")
    
    print(f"\n📊 總結:")
    print(f"   成功創建 {created_count} 個測試錄影檔案")
    print(f"   檔案位於各頻道的儲存路徑下的 {today} 目錄中")
    
    print(f"\n🌐 現在可以啟動Web介面測試歷史回放功能:")
    print(f"   python start_web_server.py")
    print(f"   然後在瀏覽器中訪問 http://localhost:8080/playback")

def clean_test_recordings():
    """清理測試錄影檔案"""
    print("🧹 清理測試錄影檔案...")
    
    if not os.path.exists('settings_free.json'):
        print("❌ 找不到配置文件")
        return
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            channels_config = json.load(f)
    except Exception as e:
        print(f"❌ 讀取配置文件失敗: {e}")
        return
    
    cleaned_count = 0
    today = datetime.now().strftime('%Y%m%d')
    
    for channel_data in channels_config:
        save_path = channel_data.get('save_path', './recordings')
        date_dir = os.path.join(save_path, today)
        
        if os.path.exists(date_dir):
            for filename in os.listdir(date_dir):
                if filename.endswith('.mp4') and 'TEST' in filename:
                    file_path = os.path.join(date_dir, filename)
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                        print(f"   🗑️  刪除: {filename}")
                    except Exception as e:
                        print(f"   ❌ 刪除失敗 {filename}: {e}")
    
    print(f"✅ 清理完成，刪除了 {cleaned_count} 個測試檔案")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        clean_test_recordings()
    else:
        create_test_recordings_for_channels()