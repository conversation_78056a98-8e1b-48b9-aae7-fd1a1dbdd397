{% extends "base.html" %}

{% block title %}系統設定 - NVRV-Free{% endblock %}

{% block page_title %}系統設定{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-success" onclick="saveAllSettings()">
        <i class="fas fa-save me-1"></i>
        儲存設定
    </button>
    <button type="button" class="btn btn-outline-primary" onclick="loadSettings()">
        <i class="fas fa-sync-alt me-1"></i>
        重新載入
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>注意:</strong> 修改設定後需要重新啟動錄影才會生效。
        </div>
    </div>
</div>

<div class="row">
    <!-- 系統設定 -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    系統設定
                </h5>
            </div>
            <div class="card-body">
                <form id="systemSettingsForm">
                    <div class="mb-3">
                        <label for="webPort" class="form-label">Web介面埠號</label>
                        <input type="number" class="form-control" id="webPort" value="8080" min="1024" max="65535">
                        <div class="form-text">預設: 8080</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxChannels" class="form-label">最大頻道數</label>
                        <input type="number" class="form-control" id="maxChannels" value="50" min="1" max="100">
                        <div class="form-text">系統支援的最大頻道數量</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="logLevel" class="form-label">日誌等級</label>
                        <select class="form-select" id="logLevel">
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO" selected>INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoStart" checked>
                            <label class="form-check-label" for="autoStart">
                                系統啟動時自動開始錄影
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 儲存設定 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-hdd me-2"></i>
                    儲存設定
                </h5>
            </div>
            <div class="card-body">
                <form id="storageSettingsForm">
                    <div class="mb-3">
                        <label for="defaultSavePath" class="form-label">預設儲存路徑</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="defaultSavePath" value="./recordings">
                            <button class="btn btn-outline-secondary" type="button" onclick="selectFolder()">
                                <i class="fas fa-folder-open"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="defaultKeepDays" class="form-label">預設保留天數</label>
                        <input type="number" class="form-control" id="defaultKeepDays" value="7" min="1" max="365">
                        <div class="form-text">自動刪除超過指定天數的錄影檔案</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="diskSpaceThreshold" class="form-label">磁碟空間警告閾值 (%)</label>
                        <input type="number" class="form-control" id="diskSpaceThreshold" value="90" min="50" max="99">
                        <div class="form-text">磁碟使用率超過此值時發出警告</div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 頻道管理 -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-video me-2"></i>
                    頻道管理
                </h5>
                <button class="btn btn-sm btn-primary" onclick="addChannel()">
                    <i class="fas fa-plus me-1"></i>
                    新增頻道
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>頻道名稱</th>
                                <th>狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="channelTableBody">
                            <tr>
                                <td colspan="3" class="text-center text-muted p-4">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    載入中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 編碼設定 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-film me-2"></i>
                    預設編碼設定
                </h5>
            </div>
            <div class="card-body">
                <form id="encodingSettingsForm">
                    <div class="mb-3">
                        <label for="defaultResolution" class="form-label">預設解析度</label>
                        <select class="form-select" id="defaultResolution">
                            <option value="640x360" selected>640x360 (節省空間)</option>
                            <option value="original">保持原始解析度</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="defaultFPS" class="form-label">預設FPS</label>
                        <input type="number" class="form-control" id="defaultFPS" value="8" min="1" max="30">
                    </div>
                    
                    <div class="mb-3">
                        <label for="defaultDuration" class="form-label">預設錄影時長 (分鐘)</label>
                        <input type="number" class="form-control" id="defaultDuration" value="20" min="1" max="120">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableH264Optimization" checked>
                            <label class="form-check-label" for="enableH264Optimization">
                                啟用H.264 CABAC+GOP優化
                            </label>
                            <div class="form-text">可額外節省20-30%儲存空間</div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 頻道設定模態框 -->
<div class="modal fade" id="channelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="channelModalTitle">新增頻道</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="channelForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channelName" class="form-label">頻道名稱 *</label>
                                <input type="text" class="form-control" id="channelName" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="rtspUrl" class="form-label">RTSP URL *</label>
                                <input type="url" class="form-control" id="rtspUrl" required>
                                <div class="form-text">例如: rtsp://user:pass@192.168.1.100:554/stream</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="savePath" class="form-label">儲存路徑</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="savePath" value="./recordings">
                                    <button class="btn btn-outline-secondary" type="button" onclick="selectChannelFolder()">
                                        <i class="fas fa-folder-open"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recordDuration" class="form-label">錄影時長 (分鐘)</label>
                                <input type="number" class="form-control" id="recordDuration" value="20" min="1" max="120">
                            </div>
                            
                            <div class="mb-3">
                                <label for="recordFPS" class="form-label">錄影FPS</label>
                                <input type="number" class="form-control" id="recordFPS" value="8" min="1" max="30">
                            </div>
                            
                            <div class="mb-3">
                                <label for="keepDays" class="form-label">保留天數</label>
                                <input type="number" class="form-control" id="keepDays" value="7" min="1" max="365">
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="useSourceFPS">
                                    <label class="form-check-label" for="useSourceFPS">
                                        使用原始串流FPS
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="force640x360" checked>
                                    <label class="form-check-label" for="force640x360">
                                        強制640x360解析度 (節省空間)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveChannel()">儲存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let channels = [];
let currentChannelId = null;
let isEditMode = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadChannels();
});

// 載入設定
function loadSettings() {
    // TODO: 從伺服器載入系統設定
    console.log('載入系統設定...');
}

// 載入頻道列表
async function loadChannels() {
    try {
        const response = await fetch('/api/channels');
        const data = await response.json();
        
        if (data.success) {
            channels = data.channels;
            renderChannelTable();
        }
    } catch (error) {
        console.error('載入頻道失敗:', error);
    }
}

// 渲染頻道表格
function renderChannelTable() {
    const tbody = document.getElementById('channelTableBody');
    
    if (channels.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center text-muted p-4">
                    <i class="fas fa-video-slash me-2"></i>
                    尚未設定任何頻道
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = channels.map(channel => `
        <tr>
            <td>
                <div>
                    <strong>${channel.name}</strong>
                    <br>
                    <small class="text-muted">${channel.rtsp_url}</small>
                </div>
            </td>
            <td>
                <span class="badge bg-${channel.status === 'online' ? 'success' : 'secondary'}">
                    ${channel.status === 'online' ? '線上' : '離線'}
                </span>
                ${channel.recording ? '<span class="badge bg-danger ms-1">錄影中</span>' : ''}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editChannel(${channel.id})" title="編輯">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="testChannel(${channel.id})" title="測試連線">
                        <i class="fas fa-plug"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteChannel(${channel.id})" title="刪除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 新增頻道
function addChannel() {
    isEditMode = false;
    currentChannelId = null;
    document.getElementById('channelModalTitle').textContent = '新增頻道';
    
    // 清空表單
    document.getElementById('channelForm').reset();
    document.getElementById('savePath').value = './recordings';
    document.getElementById('recordDuration').value = '20';
    document.getElementById('recordFPS').value = '8';
    document.getElementById('keepDays').value = '7';
    document.getElementById('force640x360').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('channelModal'));
    modal.show();
}

// 編輯頻道
function editChannel(channelId) {
    isEditMode = true;
    currentChannelId = channelId;
    document.getElementById('channelModalTitle').textContent = '編輯頻道';
    
    const channel = channels.find(c => c.id === channelId);
    if (channel) {
        // TODO: 載入頻道詳細設定
        document.getElementById('channelName').value = channel.name;
        document.getElementById('rtspUrl').value = channel.rtsp_url;
        
        const modal = new bootstrap.Modal(document.getElementById('channelModal'));
        modal.show();
    }
}

// 儲存頻道
async function saveChannel() {
    const formData = {
        name: document.getElementById('channelName').value,
        rtsp_url: document.getElementById('rtspUrl').value,
        save_path: document.getElementById('savePath').value,
        record_duration: parseInt(document.getElementById('recordDuration').value),
        record_fps: parseInt(document.getElementById('recordFPS').value),
        keep_days: parseInt(document.getElementById('keepDays').value),
        use_source_fps: document.getElementById('useSourceFPS').checked,
        force_640x360: document.getElementById('force640x360').checked
    };
    
    // 驗證表單
    if (!formData.name || !formData.rtsp_url) {
        alert('請填寫必要欄位');
        return;
    }
    
    try {
        // TODO: 發送到伺服器儲存
        console.log('儲存頻道:', formData);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('channelModal'));
        modal.hide();
        
        // 重新載入頻道列表
        await loadChannels();
        
        alert(isEditMode ? '頻道設定已更新' : '頻道已新增');
    } catch (error) {
        alert('儲存失敗: ' + error.message);
    }
}

// 測試頻道連線
async function testChannel(channelId) {
    const channel = channels.find(c => c.id === channelId);
    if (!channel) return;
    
    // TODO: 實現連線測試
    alert(`正在測試頻道 "${channel.name}" 的連線...`);
}

// 刪除頻道
async function deleteChannel(channelId) {
    const channel = channels.find(c => c.id === channelId);
    if (!channel) return;
    
    if (confirm(`確定要刪除頻道 "${channel.name}" 嗎？`)) {
        try {
            // TODO: 發送刪除請求到伺服器
            console.log('刪除頻道:', channelId);
            
            // 重新載入頻道列表
            await loadChannels();
            
            alert('頻道已刪除');
        } catch (error) {
            alert('刪除失敗: ' + error.message);
        }
    }
}

// 選擇資料夾
function selectFolder() {
    // TODO: 實現資料夾選擇
    alert('資料夾選擇功能開發中...');
}

function selectChannelFolder() {
    // TODO: 實現頻道資料夾選擇
    alert('資料夾選擇功能開發中...');
}

// 儲存所有設定
async function saveAllSettings() {
    try {
        // 收集所有設定
        const settings = {
            system: {
                web_port: parseInt(document.getElementById('webPort').value),
                max_channels: parseInt(document.getElementById('maxChannels').value),
                log_level: document.getElementById('logLevel').value,
                auto_start: document.getElementById('autoStart').checked
            },
            storage: {
                default_save_path: document.getElementById('defaultSavePath').value,
                default_keep_days: parseInt(document.getElementById('defaultKeepDays').value),
                disk_space_threshold: parseInt(document.getElementById('diskSpaceThreshold').value)
            },
            encoding: {
                default_resolution: document.getElementById('defaultResolution').value,
                default_fps: parseInt(document.getElementById('defaultFPS').value),
                default_duration: parseInt(document.getElementById('defaultDuration').value),
                enable_h264_optimization: document.getElementById('enableH264Optimization').checked
            }
        };
        
        // TODO: 發送到伺服器儲存
        console.log('儲存系統設定:', settings);
        
        alert('設定已儲存');
    } catch (error) {
        alert('儲存失敗: ' + error.message);
    }
}
</script>
{% endblock %}