#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終按鈕功能測試
"""

import requests
import time
import threading
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8090, debug=False)

def main():
    print("=" * 60)
    print("NVRV-Free 歷史回放按鈕最終測試")
    print("=" * 60)
    
    # 啟動服務器
    print("🚀 啟動Web服務器...")
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    time.sleep(2)
    
    try:
        # 測試主頁面
        print("\n🌐 測試主頁面...")
        response = requests.get('http://127.0.0.1:8090/', timeout=5)
        
        if response.status_code == 200:
            content = response.text
            print("✅ 主頁面載入成功")
            
            # 檢查全局函數定義
            if 'window.goToPlayback = function(' in content:
                print("✅ goToPlayback 已定義為全局函數")
            else:
                print("❌ goToPlayback 未正確定義為全局函數")
            
            if 'window.goToChannelPlayback = function(' in content:
                print("✅ goToChannelPlayback 已定義為全局函數")
            else:
                print("❌ goToChannelPlayback 未正確定義為全局函數")
            
            # 檢查調試代碼
            if 'console.log(\'History button clicked' in content:
                print("✅ 列表模式按鈕包含調試代碼")
            else:
                print("❌ 列表模式按鈕缺少調試代碼")
            
            if 'console.log(\'Preview modal history button clicked' in content:
                print("✅ 預覽模式按鈕包含調試代碼")
            else:
                print("❌ 預覽模式按鈕缺少調試代碼")
                
        else:
            print(f"❌ 主頁面載入失敗: {response.status_code}")
            return
        
        # 測試歷史回放頁面
        print(f"\n📹 測試歷史回放頁面...")
        response = requests.get('http://127.0.0.1:8090/playback', timeout=5)
        
        if response.status_code == 200:
            print("✅ 歷史回放頁面載入成功")
        else:
            print(f"❌ 歷史回放頁面載入失敗: {response.status_code}")
        
        # 測試帶參數的歷史回放頁面
        print(f"\n🎯 測試帶參數的歷史回放頁面...")
        response = requests.get('http://127.0.0.1:8090/playback?channel=0', timeout=5)
        
        if response.status_code == 200:
            print("✅ 帶參數的歷史回放頁面載入成功")
        else:
            print(f"❌ 帶參數的歷史回放頁面載入失敗: {response.status_code}")
        
        print(f"\n" + "=" * 60)
        print("✅ 歷史回放按鈕修復完成")
        print(f"\n🔧 修復內容:")
        print("   ✅ 將函數定義移至全局作用域 (window.goToPlayback)")
        print("   ✅ 添加詳細的調試日誌")
        print("   ✅ 改進錯誤處理")
        print("   ✅ 確保函數在頁面載入時可用")
        
        print(f"\n🧪 測試步驟:")
        print("1. 打開瀏覽器: http://localhost:8080")
        print("2. 按F12打開開發者工具")
        print("3. 點擊任意頻道的「歷史回放」按鈕")
        print("4. 查看控制台輸出，應該看到:")
        print("   - 'History button clicked for channel X'")
        print("   - 'goToPlayback called with channelId: X'")
        print("   - 'Navigating to: /playback?channel=X'")
        print("5. 頁面應該跳轉到歷史回放頁面")
        
        print(f"\n🌐 如果按鈕仍然沒有反應，請檢查:")
        print("   • 瀏覽器控制台是否有JavaScript錯誤")
        print("   • 按鈕是否被其他元素覆蓋")
        print("   • 網路連接是否正常")
        print("   • 在控制台手動執行: window.goToPlayback(0)")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == '__main__':
    main()