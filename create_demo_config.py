#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建演示配置文件
用於測試Web介面功能
"""

import json
import os

def create_demo_config():
    """創建演示配置"""
    
    # 備份原始配置
    if os.path.exists('settings_free.json'):
        import shutil
        shutil.copy('settings_free.json', 'settings_free.json.backup')
        print("已備份原始配置到 settings_free.json.backup")
    
    # 創建演示配置
    demo_config = []
    
    # 添加一些真實的RTSP頻道（從原配置中取得）
    real_channels = [
        {
            "rtsp_url": "rtsp://root:Abc_123@172.23.49.34:554/axis-media/media.amp?videocodec=h264&resolution=640x480&fps=8",
            "channel_name": "洪圳路-太平西路岔口",
            "record_duration": 30,
            "save_path": "D:/rec/a",
            "keep_days": 100,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "rtsp_url": "rtsp://root:Abc_123@172.23.80.27:554/axis-media/media.amp?videocodec=h264&resolution=640x480&fps=8",
            "channel_name": "油管路二段山腳橋前",
            "record_duration": 30,
            "save_path": "D:/rec/b",
            "keep_days": 100,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "rtsp_url": "rtsp://root:Abc_123@172.23.80.28:554/axis-media/media.amp?videocodec=h264&resolution=640x480&fps=8",
            "channel_name": "油管路一段長興路三段18巷",
            "record_duration": 30,
            "save_path": "D:/rec/c",
            "keep_days": 100,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        }
    ]
    
    # 添加演示頻道
    demo_channels = [
        {
            "rtsp_url": "demo://channel1",
            "channel_name": "演示頻道 1 - 交通監控",
            "record_duration": 30,
            "save_path": "./recordings/demo1",
            "keep_days": 7,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "rtsp_url": "demo://channel2",
            "channel_name": "演示頻道 2 - 停車場",
            "record_duration": 30,
            "save_path": "./recordings/demo2",
            "keep_days": 7,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "rtsp_url": "demo://channel3",
            "channel_name": "演示頻道 3 - 大門口",
            "record_duration": 30,
            "save_path": "./recordings/demo3",
            "keep_days": 7,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "rtsp_url": "demo://channel4",
            "channel_name": "演示頻道 4 - 後門",
            "record_duration": 30,
            "save_path": "./recordings/demo4",
            "keep_days": 7,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        }
    ]
    
    # 添加空白頻道
    empty_channels = []
    for i in range(5, 21):  # 頻道5-20
        empty_channels.append({
            "rtsp_url": "",
            "channel_name": f"頻道 {i}",
            "record_duration": 30,
            "save_path": f"./recordings/channel{i}",
            "keep_days": 7,
            "record_fps": 8,
            "use_source_fps": False,
            "force_640x360": True
        })
    
    # 組合所有頻道
    demo_config.extend(real_channels)
    demo_config.extend(demo_channels)
    demo_config.extend(empty_channels)
    
    # 寫入配置文件
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, ensure_ascii=False, indent=4)
    
    print(f"已創建演示配置，包含 {len(demo_config)} 個頻道:")
    print(f"- 真實RTSP頻道: {len(real_channels)} 個")
    print(f"- 演示頻道: {len(demo_channels)} 個")
    print(f"- 空白頻道: {len(empty_channels)} 個")
    print("\n演示頻道使用 demo:// 協議，會顯示動態演示畫面")
    print("真實RTSP頻道會嘗試連接實際的攝影機")
    print("空白頻道沒有RTSP URL，會顯示離線狀態")

def restore_original_config():
    """恢復原始配置"""
    if os.path.exists('settings_free.json.backup'):
        import shutil
        shutil.copy('settings_free.json.backup', 'settings_free.json')
        print("已恢復原始配置")
    else:
        print("找不到備份配置文件")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'restore':
        restore_original_config()
    else:
        create_demo_config()