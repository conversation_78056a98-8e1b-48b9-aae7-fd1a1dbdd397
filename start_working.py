#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
確保能正常工作的Web介面啟動腳本
"""

import json
import os
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_settings():
    """驗證設定檔"""
    if not os.path.exists('settings_free.json'):
        logger.error("settings_free.json 不存在")
        return False
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        logger.info(f"讀取到 {len(settings)} 個頻道")
        for i, channel in enumerate(settings):
            name = channel.get('channel_name', 'Unknown')
            url = channel.get('rtsp_url', 'No URL')
            logger.info(f"頻道 {i}: {name} - {url}")
        
        return True
    except Exception as e:
        logger.error(f"設定檔讀取失敗: {e}")
        return False

def main():
    print("🚀 NVRV-Free Web介面 (工作版本)")
    print("=" * 50)
    
    # 驗證設定檔
    if not verify_settings():
        print("❌ 設定檔驗證失敗")
        return
    
    # 創建必要目錄
    os.makedirs('./recordings', exist_ok=True)
    os.makedirs('./templates', exist_ok=True)
    
    try:
        # 導入並啟動Web介面
        from web_interface import NVRVWebInterface
        
        print("🌐 啟動Web服務器...")
        print("   網址: http://localhost:8080")
        print("   模式: 演示模式 (動態畫面)")
        print("   按 Ctrl+C 停止")
        print("=" * 50)
        
        web_interface = NVRVWebInterface()
        web_interface.run(host='127.0.0.1', port=8080, debug=False)
        
    except KeyboardInterrupt:
        print("\n⏹️  Web服務器已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()