# -*- mode: python ; coding: utf-8 -*- 
 
block_cipher = None 
 
a = Analysis( 
    ['start_web_server.py'], 
    pathex=[], 
    binaries=[], 
    datas=[ 
        ('templates', 'templates'), 
        ('static', 'static'), 
        ('settings_free.json', '.'), 
        ('key.ico', '.') 
    ], 
    hiddenimports=[ 
        'flask_socketio.async_mode_threading', 
        'flask_socketio.namespace', 
        'engineio.async_drivers.threading', 
        'socketio.namespace', 
        'threading', 
        'queue', 
        'eventlet', 
        'eventlet.wsgi', 
        'eventlet.green', 
        'eventlet.green.threading', 
        'babel.numbers', 
        'babel.dates', 
        'pystray', 
        'PIL.Image', 
        'PIL.ImageDraw', 
        'PIL.ImageFont', 
        'cv2', 
        'sqlite3', 
        'werkzeug.security', 
        'flask_login', 
        'nvrv_web_server', 
        'json', 
        'logging', 
        'datetime', 
        'time', 
        'base64', 
        'glob', 
        'pathlib', 
        'mimetypes', 
        'numpy', 
        'functools' 
    ], 
    hookspath=[], 
    hooksconfig={}, 
    runtime_hooks=[], 
    excludes=[], 
    win_no_prefer_redirects=False, 
    win_private_assemblies=False, 
    cipher=block_cipher, 
    noarchive=False, 
) 
 
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher) 
 
exe = EXE( 
    pyz, 
    a.scripts, 
    a.binaries, 
    a.zipfiles, 
    a.datas, 
    [], 
    name='NVRV_WebServer', 
    debug=False, 
    bootloader_ignore_signals=False, 
    strip=False, 
    upx=True, 
    upx_exclude=[], 
    runtime_tmpdir=None, 
    console=False, 
    disable_windowed_traceback=False, 
    target_arch=None, 
    codesign_identity=None, 
    entitlements_file=None, 
    icon='key.ico', 
) 
