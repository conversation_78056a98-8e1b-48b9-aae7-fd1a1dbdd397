<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NVR System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa; /* 淺灰色背景 */
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #6c757d 0%, #343a40 100%);
            padding-top: 1rem;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .sidebar .user-info {
            color: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 2rem;
        }
        .channel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1rem;
        }
        .channel-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .channel-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .video-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            background: #000;
        }
        .video-stream {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .channel-status {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
            color: white;
            background: rgba(0,0,0,0.5);
        }
        .status-online { background-color: #28a745; }
        .status-connecting { background-color: #ffc107; }
        .status-reconnecting { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .status-offline { background-color: #6c757d; }



        @media (max-width: 768px) {
            .sidebar { display: none; }
            .main-content { margin-left: 0 !important; }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <div class="sidebar col-md-3 col-lg-2 d-flex flex-column p-3">
            <a href="/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                <i class="fas fa-video me-2"></i>
                <span class="fs-4">NVR System</span>
            </a>
            <hr class="text-white">
            <ul class="nav nav-pills flex-column mb-auto">
                <li class="nav-item">
                    <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                        <i class="fas fa-th-large me-2"></i> 即時監控
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('playback') }}" class="nav-link {% if request.endpoint == 'playback' %}active{% endif %}">
                        <i class="fas fa-history me-2"></i> 歷史回放
                    </a>
                </li>
                {% if current_user.role == 'admin' %}
                <li>
                    <a href="{{ url_for('user_management') }}" class="nav-link {% if request.endpoint == 'user_management' %}active{% endif %}">
                        <i class="fas fa-users-cog me-2"></i> 使用者管理
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('activity_log_page') }}" class="nav-link {% if request.endpoint == 'activity_log_page' %}active{% endif %}">
                        <i class="fas fa-clipboard-list me-2"></i> 活動日誌
                    </a>
                </li>
                {% endif %}
                {% if current_user.is_authenticated %}
                <li class="mt-auto">
                    <hr class="text-white">
                    <a href="{{ url_for('logout') }}" class="nav-link">
                        <i class="fas fa-sign-out-alt me-2"></i> 登出 ({{ current_user.username }})
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>

        <div class="main-content flex-grow-1">
            <div class="container-fluid">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        const socket = io();
        socket.on('connect', function() {
            console.log('Connected to server via WebSocket.');
        });


    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>