#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試影片播放功能的腳本
檢查 Web 介面的影片播放是否正常工作
"""

import os
import json
import glob
import requests
import sys
from datetime import datetime

def test_video_files_exist():
    """測試影片檔案是否存在"""
    print("=== 測試影片檔案存在性 ===")
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        total_files = 0
        channels_with_files = 0
        
        for i, channel in enumerate(config):
            channel_name = channel.get('channel_name', f'Channel {i+1}')
            save_path = channel.get('save_path', './recordings')
            
            if not os.path.exists(save_path):
                print(f"❌ 頻道 {i+1} ({channel_name}): 儲存路徑不存在 - {save_path}")
                continue
            
            # 搜尋所有 mp4 檔案
            mp4_files = []
            try:
                for root, dirs, files in os.walk(save_path):
                    for file in files:
                        if file.lower().endswith('.mp4'):
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            if file_size > 1024:  # 大於 1KB
                                mp4_files.append((file_path, file_size))
            except Exception as e:
                print(f"❌ 頻道 {i+1} ({channel_name}): 掃描錯誤 - {e}")
                continue
            
            if mp4_files:
                channels_with_files += 1
                total_files += len(mp4_files)
                print(f"✅ 頻道 {i+1} ({channel_name}): 找到 {len(mp4_files)} 個影片檔案")
                
                # 顯示最新的 3 個檔案
                mp4_files.sort(key=lambda x: os.path.getmtime(x[0]), reverse=True)
                for j, (file_path, file_size) in enumerate(mp4_files[:3]):
                    filename = os.path.basename(file_path)
                    size_mb = file_size / (1024 * 1024)
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    print(f"   {j+1}. {filename} ({size_mb:.1f} MB, {mtime.strftime('%Y-%m-%d %H:%M')})")
                
                if len(mp4_files) > 3:
                    print(f"   ... 還有 {len(mp4_files) - 3} 個檔案")
            else:
                print(f"⚠️  頻道 {i+1} ({channel_name}): 沒有找到影片檔案")
        
        print(f"\n📊 統計:")
        print(f"   有影片檔案的頻道: {channels_with_files}")
        print(f"   總影片檔案數: {total_files}")
        
        return total_files > 0
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def test_web_server_api():
    """測試 Web 服務器 API"""
    print("\n=== 測試 Web 服務器 API ===")
    
    base_url = "http://localhost:8080"
    
    # 測試服務器是否運行
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Web 服務器正在運行")
        else:
            print(f"⚠️  Web 服務器回應異常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Web 服務器未運行")
        print("💡 請先執行: python start_web_server.py")
        return False
    except Exception as e:
        print(f"❌ 連接 Web 服務器失敗: {e}")
        return False
    
    # 測試頻道 API
    try:
        response = requests.get(f"{base_url}/api/channels", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"✅ 頻道 API 正常，載入 {len(channels)} 個頻道")
                
                # 測試錄影 API
                for i, channel in enumerate(channels[:3]):  # 只測試前3個頻道
                    channel_id = channel.get('id')
                    channel_name = channel.get('name')
                    
                    try:
                        today = datetime.now().strftime('%Y%m%d')
                        response = requests.get(
                            f"{base_url}/api/recordings?channel_id={channel_id}&date={today}",
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data.get('success'):
                                recordings = data.get('recordings', [])
                                print(f"✅ 頻道 {channel_id} ({channel_name}): 找到 {len(recordings)} 個錄影")
                                
                                # 測試影片播放 API
                                if recordings:
                                    test_filename = recordings[0]['filename']
                                    try:
                                        response = requests.head(
                                            f"{base_url}/api/video/{test_filename}",
                                            timeout=10
                                        )
                                        if response.status_code == 200:
                                            content_length = response.headers.get('Content-Length', '0')
                                            print(f"✅ 影片播放 API 正常: {test_filename} ({content_length} bytes)")
                                        else:
                                            print(f"❌ 影片播放 API 錯誤: {response.status_code}")
                                    except Exception as e:
                                        print(f"❌ 測試影片播放失敗: {e}")
                            else:
                                print(f"⚠️  頻道 {channel_id} 錄影 API 錯誤: {data.get('error')}")
                        else:
                            print(f"❌ 頻道 {channel_id} 錄影 API 失敗: {response.status_code}")
                    except Exception as e:
                        print(f"❌ 測試頻道 {channel_id} 失敗: {e}")
                
                return True
            else:
                print(f"❌ 頻道 API 錯誤: {data.get('error')}")
                return False
        else:
            print(f"❌ 頻道 API 失敗: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 測試頻道 API 失敗: {e}")
        return False

def test_video_formats():
    """測試影片格式支援"""
    print("\n=== 測試影片格式支援 ===")
    
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        format_count = {}
        
        for channel in config:
            save_path = channel.get('save_path', './recordings')
            
            if not os.path.exists(save_path):
                continue
            
            try:
                for root, dirs, files in os.walk(save_path):
                    for file in files:
                        ext = os.path.splitext(file)[1].lower()
                        if ext in ['.mp4', '.avi', '.mov', '.mkv']:
                            format_count[ext] = format_count.get(ext, 0) + 1
            except Exception as e:
                continue
        
        if format_count:
            print("📹 發現的影片格式:")
            for ext, count in format_count.items():
                print(f"   {ext}: {count} 個檔案")
            
            # 檢查是否主要是 MP4 格式
            mp4_count = format_count.get('.mp4', 0)
            total_count = sum(format_count.values())
            mp4_ratio = mp4_count / total_count if total_count > 0 else 0
            
            if mp4_ratio > 0.8:
                print("✅ 主要使用 MP4 格式，播放相容性良好")
            else:
                print("⚠️  包含多種影片格式，可能影響播放相容性")
            
            return True
        else:
            print("❌ 沒有找到任何影片檔案")
            return False
            
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        return False

def provide_troubleshooting_tips():
    """提供故障排除建議"""
    print("\n=== 故障排除建議 ===")
    
    print("🔧 如果播放按鈕無法工作，請檢查:")
    print("1. Web 服務器是否正在運行")
    print("   - 執行: python start_web_server.py")
    print("   - 檢查控制台是否有錯誤訊息")
    
    print("\n2. 影片檔案是否存在且可讀取")
    print("   - 檢查錄影目錄權限")
    print("   - 確認檔案沒有被其他程式鎖定")
    
    print("\n3. 瀏覽器相容性")
    print("   - 建議使用 Chrome 或 Firefox")
    print("   - 檢查瀏覽器控制台的錯誤訊息")
    print("   - 嘗試重新整理頁面")
    
    print("\n4. 網路連接")
    print("   - 確認可以訪問 http://localhost:8080")
    print("   - 檢查防火牆設定")
    
    print("\n5. 影片格式")
    print("   - MP4 格式相容性最好")
    print("   - 檢查影片檔案是否損壞")
    
    print("\n🚀 測試播放功能:")
    print("1. 開啟瀏覽器到 http://localhost:8080/playback")
    print("2. 選擇有錄影檔案的頻道")
    print("3. 選擇日期")
    print("4. 點擊播放按鈕")
    print("5. 檢查瀏覽器開發者工具的網路標籤")

def main():
    """主函數"""
    print("NVRV-Free 影片播放功能測試")
    print("=" * 50)
    
    # 測試影片檔案
    files_exist = test_video_files_exist()
    
    # 測試 Web API
    api_works = test_web_server_api()
    
    # 測試影片格式
    formats_ok = test_video_formats()
    
    # 提供故障排除建議
    provide_troubleshooting_tips()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    
    if files_exist:
        print("✅ 影片檔案存在")
    else:
        print("❌ 沒有找到影片檔案")
    
    if api_works:
        print("✅ Web API 正常工作")
    else:
        print("❌ Web API 有問題")
    
    if formats_ok:
        print("✅ 影片格式支援良好")
    else:
        print("❌ 影片格式有問題")
    
    if files_exist and api_works and formats_ok:
        print("\n🎉 播放功能應該正常工作！")
        print("如果仍有問題，請檢查瀏覽器控制台的錯誤訊息")
        return 0
    else:
        print("\n⚠️  發現一些問題，請參考上述建議進行修復")
        return 1

if __name__ == "__main__":
    sys.exit(main())