#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
驗證Web介面是否正確讀取settings_free.json配置
"""

import json
import os
import requests
import threading
import time
from datetime import datetime

def verify_config_loading():
    """驗證配置載入"""
    print("🔍 驗證配置文件讀取...")
    
    # 檢查配置文件
    if not os.path.exists('settings_free.json'):
        print("❌ settings_free.json 不存在")
        return False
    
    # 讀取配置
    try:
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功讀取配置文件，包含 {len(config)} 個頻道")
        return config
    except Exception as e:
        print(f"❌ 讀取配置失敗: {e}")
        return False

def start_web_server():
    """啟動Web服務器"""
    from nvrv_web_server import NVRVWebServer
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8082, debug=False)

def verify_web_api(config):
    """驗證Web API"""
    print("\n🌐 啟動Web服務器進行驗證...")
    
    # 在背景啟動服務器
    server_thread = threading.Thread(target=start_web_server, daemon=True)
    server_thread.start()
    
    # 等待服務器啟動
    print("⏳ 等待服務器啟動...")
    time.sleep(5)
    
    try:
        # 測試頻道API
        print("🔍 測試頻道API...")
        response = requests.get('http://127.0.0.1:8082/api/channels', timeout=15)
        
        if response.status_code != 200:
            print(f"❌ API請求失敗: HTTP {response.status_code}")
            return False
        
        api_data = response.json()
        if not api_data.get('success'):
            print(f"❌ API回應錯誤: {api_data.get('error')}")
            return False
        
        api_channels = api_data.get('channels', [])
        print(f"✅ API返回 {len(api_channels)} 個頻道")
        
        # 比較配置和API返回的頻道
        if len(api_channels) != len(config):
            print(f"⚠️  頻道數量不符: 配置 {len(config)}, API {len(api_channels)}")
        
        print("\n📋 頻道對比驗證:")
        for i, (config_ch, api_ch) in enumerate(zip(config, api_channels)):
            config_name = config_ch.get('channel_name', f'Channel {i+1}')
            config_rtsp = config_ch.get('rtsp_url', '')
            config_path = config_ch.get('save_path', './recordings')
            
            api_name = api_ch.get('name', f'Channel {i+1}')
            api_rtsp = api_ch.get('rtsp_url', '')
            api_path = api_ch.get('save_path', './recordings')
            
            # 檢查名稱
            name_match = "✅" if config_name == api_name else "❌"
            print(f"   頻道 {i+1}: {name_match} {config_name}")
            
            # 檢查RTSP URL
            rtsp_match = "✅" if config_rtsp == api_rtsp else "❌"
            if config_rtsp:
                print(f"      RTSP: {rtsp_match} {config_rtsp[:50]}...")
            else:
                print(f"      RTSP: {rtsp_match} (未設定)")
            
            # 檢查儲存路徑
            path_match = "✅" if config_path == api_path else "❌"
            print(f"      路徑: {path_match} {config_path}")
            
            # 檢查狀態
            status = api_ch.get('status', 'unknown')
            status_icon = {
                'online': '🟢',
                'offline': '🔴', 
                'demo': '🟡',
                'error': '⚠️'
            }.get(status, '❓')
            print(f"      狀態: {status_icon} {status}")
            
            if i >= 4:  # 只顯示前5個
                remaining = len(config) - 5
                if remaining > 0:
                    print(f"   ... 還有 {remaining} 個頻道")
                break
            print()
        
        # 測試錄影API
        print("\n🎬 測試錄影API...")
        if api_channels:
            first_channel = api_channels[0]
            channel_id = first_channel.get('id', 0)
            channel_name = first_channel.get('name', 'Unknown')
            
            print(f"   測試頻道: {channel_name}")
            
            today = datetime.now().strftime('%Y%m%d')
            recordings_url = f'http://127.0.0.1:8082/api/recordings?channel_id={channel_id}&date={today}'
            
            response = requests.get(recordings_url, timeout=10)
            if response.status_code == 200:
                recordings_data = response.json()
                if recordings_data.get('success'):
                    recordings = recordings_data.get('recordings', [])
                    print(f"   ✅ 錄影API正常，找到 {len(recordings)} 個錄影檔案")
                    
                    for recording in recordings[:3]:  # 顯示前3個
                        filename = recording.get('filename', 'Unknown')
                        size = recording.get('size', 0)
                        size_mb = size / (1024 * 1024) if size > 0 else 0
                        print(f"      📹 {filename} ({size_mb:.1f}MB)")
                else:
                    print(f"   ❌ 錄影API錯誤: {recordings_data.get('error')}")
            else:
                print(f"   ❌ 錄影API請求失敗: HTTP {response.status_code}")
        
        print("\n✅ Web介面驗證完成")
        print(f"🌐 Web介面地址: http://127.0.0.1:8082")
        print("按 Ctrl+C 停止服務器")
        
        # 保持服務器運行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 服務器已停止")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 連接服務器失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 驗證過程中發生錯誤: {e}")
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("NVRV-Free Web介面配置驗證")
    print("=" * 60)
    
    # 1. 驗證配置載入
    config = verify_config_loading()
    if not config:
        return
    
    # 顯示配置摘要
    print(f"\n📊 配置摘要:")
    rtsp_count = sum(1 for ch in config if ch.get('rtsp_url', ''))
    empty_count = len(config) - rtsp_count
    
    print(f"   總頻道數: {len(config)}")
    print(f"   有RTSP的頻道: {rtsp_count}")
    print(f"   空白頻道: {empty_count}")
    
    # 顯示儲存路徑
    save_paths = set(ch.get('save_path', './recordings') for ch in config)
    print(f"   使用的儲存路徑: {len(save_paths)} 個")
    for path in sorted(save_paths):
        exists = "✅" if os.path.exists(path) else "❌"
        print(f"      {exists} {path}")
    
    # 2. 驗證Web API
    verify_web_api(config)

if __name__ == '__main__':
    main()