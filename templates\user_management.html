{% extends "base.html" %}

{% block title %}使用者管理 - NVRV-Free{% endblock %}

{% block page_title %}使用者管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">新增使用者</h6>
            </div>
            <div class="card-body">
                <form id="add-user-form">
                    <div class="mb-3">
                        <label for="new-username" class="form-label">使用者名稱</label>
                        <input type="text" class="form-control" id="new-username" required>
                    </div>
                    <div class="mb-3">
                        <label for="new-password" class="form-label">密碼</label>
                        <input type="password" class="form-control" id="new-password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new-role" class="form-label">角色</label>
                        <select class="form-select" id="new-role">
                            <option value="user">一般使用者</option>
                            <option value="admin">管理員</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">新增</button>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">使用者列表</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>使用者名稱</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="user-list-body">
                            <!-- User list will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改使用者</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="edit-user-form">
                    <input type="hidden" id="edit-user-id">
                    <div class="mb-3">
                        <label for="edit-password" class="form-label">新密碼</label>
                        <input type="password" class="form-control" id="edit-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">儲存變更</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
async function loadUsers() {
    const response = await fetch('/api/users');
    const data = await response.json();
    if (data.success) {
        const userListBody = document.getElementById('user-list-body');
        userListBody.innerHTML = data.users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.role}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="openEditModal(${user.id})">修改密碼</button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id}, '${user.username}')">刪除</button>
                </td>
            </tr>
        `).join('');
    }
}

document.getElementById('add-user-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    const username = document.getElementById('new-username').value;
    const password = document.getElementById('new-password').value;
    const role = document.getElementById('new-role').value;

    const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password, role })
    });
    const data = await response.json();
    if (data.success) {
        loadUsers();
        this.reset();
    } else {
        alert(data.error);
    }
});

let editModal;
function openEditModal(userId) {
    document.getElementById('edit-user-id').value = userId;
    editModal = new bootstrap.Modal(document.getElementById('editUserModal'));
    editModal.show();
}

document.getElementById('edit-user-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    const userId = document.getElementById('edit-user-id').value;
    const password = document.getElementById('edit-password').value;

    const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
    });
    const data = await response.json();
    if (data.success) {
        editModal.hide();
        loadUsers();
    } else {
        alert(data.error);
    }
});

async function deleteUser(userId, username) {
    if (confirm(`確定要刪除使用者 "${username}" 嗎？`)) {
        const response = await fetch(`/api/users/${userId}`, { method: 'DELETE' });
        const data = await response.json();
        if (data.success) {
            loadUsers();
        } else {
            alert(data.error);
        }
    }
}

document.addEventListener('DOMContentLoaded', loadUsers);
</script>
{% endblock %}