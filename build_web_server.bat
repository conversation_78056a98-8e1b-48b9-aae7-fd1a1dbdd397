
@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title NVRV-Free Web Server 打包工具

echo.
echo ========================================
echo    NVRV-Free Web Server 打包工具
echo ========================================
echo.

REM 檢查 Python
echo 🔍 檢查 Python 環境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，請先安裝 Python 3.8+
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo ✅ Python 版本: %python_version%

REM 檢查必要檔案
echo.
echo 🔍 檢查必要檔案...
set missing_files=0

if not exist "start_web_server.py" (
    echo ❌ 未找到 start_web_server.py
    set missing_files=1
) else (
    echo ✅ start_web_server.py
)

if not exist "nvrv_web_server.py" (
    echo ❌ 未找到 nvrv_web_server.py
    set missing_files=1
) else (
    echo ✅ nvrv_web_server.py
)

if not exist "settings_free.json" (
    echo ❌ 未找到 settings_free.json
    set missing_files=1
) else (
    echo ✅ settings_free.json
)

if not exist "key.ico" (
    echo ❌ 未找到 key.ico
    set missing_files=1
) else (
    echo ✅ key.ico
)

if not exist "templates" (
    echo ❌ 未找到 templates 目錄
    set missing_files=1
) else (
    echo ✅ templates 目錄
)

if not exist "static" (
    echo ⚠️  未找到 static 目錄 (可選)
) else (
    echo ✅ static 目錄
)

if %missing_files%==1 (
    echo.
    echo ❌ 缺少必要檔案，無法繼續打包
    echo 請確保所有必要檔案都在當前目錄中
    pause
    exit /b 1
)

echo.
echo ✅ 檔案檢查完成

REM 檢查並安裝依賴
echo.
echo 📦 檢查並安裝依賴套件...

REM 檢查 PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 📥 安裝 PyInstaller...
    pip install pyinstaller
)

REM 檢查其他依賴
echo 📥 安裝/更新必要套件...
pip install --upgrade flask flask-socketio pystray pillow opencv-python eventlet numpy flask-login werkzeug

echo ✅ 依賴套件安裝完成

REM 創建 spec 檔案
echo.
echo 📝 創建 PyInstaller 配置檔案...
echo 選擇打包模式:
echo 1. 無控制台版本 (推薦)
echo 2. 有控制台版本 (調試用)
set /p mode="請選擇 (1 或 2): "

if "!mode!"=="2" (
    set console_mode=True
    set exe_name=NVRV_WebServer_Debug
    echo 🔧 創建調試版本配置...
) else (
    set console_mode=False
    set exe_name=NVRV_WebServer
    echo 🔧 創建無控制台版本配置...
)

echo # -*- mode: python ; coding: utf-8 -*- > start_web_server.spec
echo. >> start_web_server.spec
echo block_cipher = None >> start_web_server.spec
echo. >> start_web_server.spec
echo a = Analysis( >> start_web_server.spec
echo     ['start_web_server.py'], >> start_web_server.spec
echo     pathex=[], >> start_web_server.spec
echo     binaries=[], >> start_web_server.spec
echo     datas=[ >> start_web_server.spec
echo         ('templates', 'templates'^), >> start_web_server.spec
echo         ('static', 'static'^), >> start_web_server.spec
echo         ('settings_free.json', '.'^), >> start_web_server.spec
echo         ('key.ico', '.'^) >> start_web_server.spec
echo     ], >> start_web_server.spec
echo     hiddenimports=[ >> start_web_server.spec
echo         'flask_socketio.async_mode_threading', >> start_web_server.spec
echo         'flask_socketio.namespace', >> start_web_server.spec
echo         'engineio.async_drivers.threading', >> start_web_server.spec
echo         'socketio.namespace', >> start_web_server.spec
echo         'threading', >> start_web_server.spec
echo         'queue', >> start_web_server.spec
echo         'eventlet', >> start_web_server.spec
echo         'eventlet.wsgi', >> start_web_server.spec
echo         'eventlet.green', >> start_web_server.spec
echo         'eventlet.green.threading', >> start_web_server.spec
echo         'babel.numbers', >> start_web_server.spec
echo         'babel.dates', >> start_web_server.spec
echo         'pystray', >> start_web_server.spec
echo         'PIL.Image', >> start_web_server.spec
echo         'PIL.ImageDraw', >> start_web_server.spec
echo         'PIL.ImageFont', >> start_web_server.spec
echo         'cv2', >> start_web_server.spec
echo         'sqlite3', >> start_web_server.spec
echo         'werkzeug.security', >> start_web_server.spec
echo         'flask_login', >> start_web_server.spec
echo         'nvrv_web_server', >> start_web_server.spec
echo         'json', >> start_web_server.spec
echo         'logging', >> start_web_server.spec
echo         'datetime', >> start_web_server.spec
echo         'time', >> start_web_server.spec
echo         'base64', >> start_web_server.spec
echo         'glob', >> start_web_server.spec
echo         'pathlib', >> start_web_server.spec
echo         'mimetypes', >> start_web_server.spec
echo         'numpy', >> start_web_server.spec
echo         'functools' >> start_web_server.spec
echo     ], >> start_web_server.spec
echo     hookspath=[], >> start_web_server.spec
echo     hooksconfig={}, >> start_web_server.spec
echo     runtime_hooks=[], >> start_web_server.spec
echo     excludes=[], >> start_web_server.spec
echo     win_no_prefer_redirects=False, >> start_web_server.spec
echo     win_private_assemblies=False, >> start_web_server.spec
echo     cipher=block_cipher, >> start_web_server.spec
echo     noarchive=False, >> start_web_server.spec
echo ^) >> start_web_server.spec
echo. >> start_web_server.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher^) >> start_web_server.spec
echo. >> start_web_server.spec
echo exe = EXE( >> start_web_server.spec
echo     pyz, >> start_web_server.spec
echo     a.scripts, >> start_web_server.spec
echo     a.binaries, >> start_web_server.spec
echo     a.zipfiles, >> start_web_server.spec
echo     a.datas, >> start_web_server.spec
echo     [], >> start_web_server.spec
echo     name='!exe_name!', >> start_web_server.spec
echo     debug=False, >> start_web_server.spec
echo     bootloader_ignore_signals=False, >> start_web_server.spec
echo     strip=False, >> start_web_server.spec
echo     upx=True, >> start_web_server.spec
echo     upx_exclude=[], >> start_web_server.spec
echo     runtime_tmpdir=None, >> start_web_server.spec
echo     console=!console_mode!, >> start_web_server.spec
echo     disable_windowed_traceback=False, >> start_web_server.spec
echo     target_arch=None, >> start_web_server.spec
echo     codesign_identity=None, >> start_web_server.spec
echo     entitlements_file=None, >> start_web_server.spec
echo     icon='key.ico', >> start_web_server.spec
echo ^) >> start_web_server.spec

echo ✅ 配置檔案已創建: start_web_server.spec

REM 清理舊檔案
echo.
echo 🧹 清理舊檔案...
if exist "dist" (
    echo 刪除舊的 dist 目錄...
    rmdir /s /q "dist"
)
if exist "build" (
    echo 刪除舊的 build 目錄...
    rmdir /s /q "build"
)
if exist "__pycache__" (
    rmdir /s /q "__pycache__"
)

echo ✅ 清理完成

REM 開始打包
echo.
echo 🚀 開始打包...
echo ========================================
echo 主程式: start_web_server.py
echo 配置檔: start_web_server.spec
echo 目標檔: NVRV_WebServer.exe
echo 模式: 無控制台視窗 (系統托盤運行)
echo ========================================
echo.
echo 打包中，請稍候...

pyinstaller --clean start_web_server.spec

REM 檢查結果
echo.
echo ========================================
if exist "dist\!exe_name!.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 檔案位置: dist\!exe_name!.exe
    
    REM 計算檔案大小
    for %%A in ("dist\!exe_name!.exe") do (
        set size=%%~zA
        set /a size_mb=!size!/1024/1024
        echo 📏 檔案大小: !size_mb! MB ^(!size! bytes^)
    )
    
    echo.
    echo 📋 部署說明:
    echo ========================================
    echo 1. 複製以下檔案到目標電腦:
    echo    • dist\!exe_name!.exe
    echo    • settings_free.json
    echo.
    echo 2. 執行方式:
    echo    • 雙擊 !exe_name!.exe
    echo    • 程式會在系統托盤運行 (無視窗)
    echo    • 瀏覽器會自動開啟 Web 介面
    echo.
    echo 3. 使用說明:
    echo    • 右鍵托盤圖標可開啟介面或結束程式
    echo    • Web 介面地址: http://localhost:8080
    echo    • 錯誤日誌: nvrv_web_server.log
    echo.
    echo 4. 系統需求:
    echo    • Windows 7/10/11
    echo    • 無需安裝 Python
    echo ========================================
    echo.
    
    REM 測試選項
    set /p test="是否立即測試 EXE？(y/n): "
    if /i "!test!"=="y" (
        echo.
        echo 🧪 啟動測試...
        echo 💡 程式會在系統托盤運行，請檢查右下角圖標
        echo 💡 如果看不到，請點擊「顯示隱藏的圖標」
        echo.
        start "" "dist\!exe_name!.exe"
        timeout /t 3 >nul
        echo ✅ 程式已啟動，請檢查:
        echo    1. 系統托盤是否有 NVRV 圖標
        echo    2. 瀏覽器是否自動開啟
        echo    3. Web 介面是否正常顯示
    )
    
) else (
    echo ❌ 打包失敗！
    echo.
    echo 🔧 可能的解決方案:
    echo ========================================
    echo 1. 檢查上方的錯誤訊息
    echo 2. 確保防毒軟體沒有阻擋 PyInstaller
    echo 3. 檢查磁碟空間是否足夠 (建議 >2GB)
    echo 4. 嘗試以管理員身分執行此批次檔
    echo 5. 檢查 Python 版本是否為 3.8+
    echo 6. 手動執行: pyinstaller --clean start_web_server.spec
    echo ========================================
)

echo.
echo 🏁 打包程序完成
pause

