{% extends "base.html" %}

{% block title %}即時監控 - NVRV-Free{% endblock %}

{% block page_title %}即時監控{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="loadChannels()">
        <i class="fas fa-sync-alt me-1"></i>
        重新整理
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info d-none" id="status-alert">
            <i class="fas fa-info-circle me-2"></i>
            <span id="status-message"></span>
        </div>
    </div>
</div>

<!-- 視圖模式切換 -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary active" id="list-view-btn" onclick="switchToListView()">
                <i class="fas fa-list me-1"></i>列表模式
            </button>
            <button type="button" class="btn btn-outline-primary" id="grid-view-btn" onclick="switchToGridView()">
                <i class="fas fa-th me-1"></i>網格模式
            </button>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <small class="text-muted">
            <i class="fas fa-circle text-success me-1"></i>就緒: <span id="ready-count">0</span>
            <i class="fas fa-circle text-danger me-1 ms-3"></i>離線: <span id="offline-count">0</span>
            <i class="fas fa-circle text-warning me-1 ms-3"></i>演示: <span id="demo-count">0</span>
        </small>
    </div>
</div>

<!-- 網格模式控制（初始隱藏） -->
<div class="row mb-3 d-none" id="grid-controls">
    <div class="col-md-6">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setGridSize(2)">2x2</button>
            <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="setGridSize(3)">3x3</button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setGridSize(4)">4x4</button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setGridSize(5)">5x5</button>
        </div>
    </div>
</div>

<!-- 頻道列表視圖（預設顯示） -->
<div id="channel-list-view">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-video me-2"></i>
                頻道列表
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="60">#</th>
                            <th>頻道名稱</th>
                            <th>RTSP狀態</th>
                            <th>儲存路徑</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody id="channel-list-body">
                        <tr>
                            <td colspan="5" class="text-center text-muted p-4">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                載入中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 頻道網格視圖（初始隱藏） -->
<div class="channel-grid d-none" id="channel-grid-view">
    <!-- 頻道卡片將由JavaScript動態載入 -->
</div>

<!-- 單一頻道預覽模態框 -->
<div class="modal fade" id="channelPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">頻道預覽</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row">
                    <div class="col-md-8">
                        <div class="video-preview-container" style="background: #000; height: 480px; position: relative;">
                            <img id="previewImage" class="img-fluid w-100 h-100" style="object-fit: contain;">
                            <div class="loading-spinner position-absolute top-50 start-50 translate-middle" id="preview-loading">
                                <div class="spinner-border text-light" role="status">
                                    <span class="visually-hidden">載入中...</span>
                                </div>
                            </div>
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success" id="preview-status">連線中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-3">
                            <h6>頻道資訊</h6>
                            <div class="mb-2">
                                <strong>名稱:</strong> <span id="preview-channel-name">-</span>
                            </div>

                            <div class="mb-2">
                                <strong>儲存路徑:</strong> 
                                <small class="text-muted d-block" id="preview-save-path">-</small>
                            </div>
                            <div class="mb-3">
                                <strong>狀態:</strong> <span id="preview-channel-status">-</span>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="openFullscreen()">
                                    <i class="fas fa-expand me-1"></i>全螢幕
                                </button>
                                <button class="btn btn-info" onclick="console.log('Preview modal history button clicked'); goToChannelPlayback()">
                                    <i class="fas fa-history me-1"></i>歷史回放
                                </button>
                                <button class="btn btn-success" onclick="testChannelConnection()">
                                    <i class="fas fa-plug me-1"></i>測試連線
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全螢幕預覽模態框 -->
<div class="modal fade" id="fullscreenModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="fullscreenTitle">頻道名稱</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0 d-flex align-items-center justify-content-center">
                <img id="fullscreenImage" class="img-fluid" style="max-height: 90vh;">
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let channels = [];
let currentChannelId = null;
let currentGridSize = 3;
let isListView = true;
let previewChannelId = null;

// 載入頻道資訊
async function loadChannels() {
    console.log('Loading channels...');
    try {
        const response = await fetch('/api/channels');
        const data = await response.json();
        
        console.log('Channels response:', data);
        
        if (data.success) {
            channels = data.channels;
            console.log(`Loaded ${channels.length} channels:`, channels);
            
            if (isListView) {
                renderChannelList();
            } else {
                renderChannelGrid();
            }
            updateStatusCounts();
        } else {
            console.error('Failed to load channels:', data.error);
            showAlert('載入頻道失敗: ' + data.error, 'danger');
        }
    } catch (error) {
        console.error('Error loading channels:', error);
        showAlert('載入頻道失敗: ' + error.message, 'danger');
    }
}

// 渲染頻道列表
function renderChannelList() {
    console.log('Rendering channel list:', channels);
    const tbody = document.getElementById('channel-list-body');
    
    if (!tbody) {
        console.error('Channel list body element not found');
        return;
    }
    
    if (channels.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted p-4">
                    <i class="fas fa-video-slash fa-2x mb-2"></i>
                    <div>尚未設定任何頻道</div>
                    <small>請先在設定頁面新增RTSP頻道</small>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = channels.map(channel => {
        const statusIcon = {
            'ready': '🟡',
            'offline': '🔴',
            'demo': '🟢',
            'online': '🟢',
            'error': '⚠️'
        }[channel.status] || '❓';
        
        const rtspStatus = channel.status === 'demo' ? '演示模式' : 
            channel.status === 'ready' ? '已設定' : 
            channel.status === 'offline' ? '離線' : '就緒';
        
        return `
            <tr class="channel-row" data-channel-id="${channel.id}">
                <td class="text-center">
                    <strong>${channel.id + 1}</strong>
                </td>
                <td>
                                            <div class="d-flex align-items-center">
                            <div>
                                <strong>${channel.name}</strong>
                            </div>
                        </div>
                </td>
                <td>
                    <span class="badge bg-${channel.status === 'ready' ? 'warning' : channel.status === 'offline' ? 'danger' : 'success'}">
                        ${statusIcon} ${rtspStatus}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${channel.save_path}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewChannel(${channel.id})" title="預覽">
                            <i class="fas fa-eye"></i> 預覽
                        </button>
                        <button class="btn btn-outline-info" onclick="console.log('History button clicked for channel ${channel.id}'); goToPlayback(${channel.id})" title="歷史回放">
                            <i class="fas fa-history"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="testStream(${channel.id})" title="測試連線">
                            <i class="fas fa-plug"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 渲染頻道網格
function renderChannelGrid() {
    console.log('Rendering channel grid:', channels);
    const grid = document.getElementById('channel-grid-view');
    
    if (!grid) {
        console.error('Channel grid element not found');
        return;
    }
    
    // 設置網格樣式
    grid.style.gridTemplateColumns = `repeat(${currentGridSize}, 1fr)`;
    
    if (channels.length === 0) {
        grid.innerHTML = `
            <div class="col-12 text-center p-5" style="grid-column: 1 / -1;">
                <i class="fas fa-video-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">尚未設定任何頻道</h5>
                <p class="text-muted">請先在設定頁面新增RTSP頻道</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = channels.map(channel => `
        <div class="channel-card" data-channel-id="${channel.id}">
            <div class="video-container">
                <img id="stream-${channel.id}" 
                     class="video-stream" 
                     alt="${channel.name}"
                     onclick="previewChannel(${channel.id})"
                     style="cursor: pointer; display: none;"
                     loading="lazy">
                
                <div class="channel-status" id="status-${channel.id}"></div>
                
                <div class="loading-spinner" id="loading-${channel.id}" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                </div>
            </div>
            
            <div class="channel-controls">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0" title="${channel.name}">${channel.name.length > 15 ? channel.name.substring(0, 15) + '...' : channel.name}</h6>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="testStream(${channel.id})" title="測試連線">
                            <i class="fas fa-plug"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="previewChannel(${channel.id})" title="預覽">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="console.log('Grid history button clicked for channel ${channel.id}'); goToPlayback(${channel.id})" title="歷史回放">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
                
                <small class="text-muted d-block">
                    <i class="fas fa-signal me-1"></i>
                    <span id="stream-info-${channel.id}">480x270</span>
                </small>
                

            </div>
        </div>
    `).join('');
    
    channels.forEach(channel => {
        socket.emit('start_stream', { channel_id: channel.id });
    });
}

// 視圖切換功能
function switchToListView() {
    isListView = true;
    document.getElementById('list-view-btn').classList.add('active');
    document.getElementById('grid-view-btn').classList.remove('active');
    document.getElementById('channel-list-view').classList.remove('d-none');
    document.getElementById('channel-grid-view').classList.add('d-none');
    document.getElementById('grid-controls').classList.add('d-none');
    
    channels.forEach(channel => {
        socket.emit('stop_stream', { channel_id: channel.id });
    });

    renderChannelList();
}

function switchToGridView() {
    isListView = false;
    document.getElementById('list-view-btn').classList.remove('active');
    document.getElementById('grid-view-btn').classList.add('active');
    document.getElementById('channel-list-view').classList.add('d-none');
    document.getElementById('channel-grid-view').classList.remove('d-none');
    document.getElementById('grid-controls').classList.remove('d-none');
    
    renderChannelGrid();
}

// 預覽頻道
function previewChannel(channelId) {
    const channel = channels.find(c => c.id === channelId);
    if (!channel) return;
    
    previewChannelId = channelId;
    
    // 設置模態框內容
    document.getElementById('previewModalTitle').textContent = `${channel.name} - 即時預覽`;
    document.getElementById('preview-channel-name').textContent = channel.name;
    document.getElementById('preview-save-path').textContent = channel.save_path;
    document.getElementById('preview-channel-status').textContent = getStatusText(channel.status);
    
    // 顯示載入狀態
    document.getElementById('preview-loading').style.display = 'block';
    document.getElementById('preview-status').textContent = '連線中...';
    document.getElementById('preview-status').className = 'badge bg-warning';
    
    // 設置預覽圖片
    const previewImg = document.getElementById('previewImage');
    previewImg.src = ""; // 清空舊的影像
    socket.emit('start_stream', { channel_id: channelId });

    // 顯示模態框
    const modal = new bootstrap.Modal(document.getElementById('channelPreviewModal'));
    modal.show();
}

// 全螢幕預覽
function openFullscreen() {
    if (previewChannelId === null) return;
    
    const channel = channels.find(c => c.id === previewChannelId);
    if (!channel) return;
    
    document.getElementById('fullscreenTitle').textContent = channel.name;
    const fullscreenImg = document.getElementById('fullscreenImage');
    fullscreenImg.src = ""; // 清空舊的影像
    socket.emit('start_stream', { channel_id: previewChannelId });

    // 關閉預覽模態框
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('channelPreviewModal'));
    if (previewModal) {
        previewModal.hide();
    }
    
    // 顯示全螢幕模態框
    const fullscreenModal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    fullscreenModal.show();
}

// 跳轉到頻道回放函數已移至全局作用域

// 測試頻道連線
function testChannelConnection() {
    if (previewChannelId === null) return;
    
    document.getElementById('preview-status').textContent = '測試中...';
    document.getElementById('preview-status').className = 'badge bg-info';
    
    testStream(previewChannelId);
}

// 更新狀態統計
function updateStatusCounts() {
    const counts = {
        ready: 0,
        offline: 0,
        demo: 0
    };
    
    channels.forEach(channel => {
        if (counts.hasOwnProperty(channel.status)) {
            counts[channel.status]++;
        }
    });
    
    document.getElementById('ready-count').textContent = counts.ready;
    document.getElementById('offline-count').textContent = counts.offline;
    document.getElementById('demo-count').textContent = counts.demo;
}



// 顯示/隱藏載入動畫
function showLoading(channelId) {
    const loading = document.getElementById(`loading-${channelId}`);
    if (loading) loading.style.display = 'block';
}

function hideLoading(channelId) {
    const loading = document.getElementById(`loading-${channelId}`);
    if (loading) loading.style.display = 'none';
}

// 測試串流連線
async function testStream(channelId) {
    const channel = channels.find(c => c.id === channelId);
    if (!channel) return;
    
    showAlert(`正在測試頻道 "${channel.name}" 的連線...`, 'info');
    
    try {
        const response = await fetch(`/api/test_stream/${channelId}`);
        const data = await response.json();
        
        if (data.success) {
            showAlert(`頻道 "${channel.name}" 連線正常`, 'success');
            updateChannelStatus(channelId, 'online');
        } else {
            showAlert(`頻道 "${channel.name}" 連線失敗: ${data.error}`, 'danger');
            updateChannelStatus(channelId, 'offline');
        }
    } catch (error) {
        showAlert(`測試失敗: ${error.message}`, 'danger');
    }
}

// 測試所有串流
async function testAllStreams() {
    showAlert('正在測試所有頻道連線...', 'info');
    
    for (const channel of channels) {
        await testStream(channel.id);
        // 避免同時發送太多請求
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}





// 設置網格大小
function setGridSize(size) {
    currentGridSize = size;
    
    // 更新按鈕狀態
    document.querySelectorAll('#grid-controls .btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 如果在網格模式，重新渲染
    if (!isListView) {
        renderChannelGrid();
    }
}

// 重新整理所有串流
function refreshAllStreams() {
    channels.forEach(channel => {
        socket.emit('start_stream', { channel_id: channel.id });
    });
    showAlert('正在重新整理所有串流...', 'info');
}

// 獲取狀態文字
function getStatusText(status) {
    const statusMap = {
        'online': '線上',
        'offline': '離線',
        'connecting': '連線中',
        'error': '錯誤',
        'demo': '演示',
        'ready': '就緒'
    };
    return statusMap[status] || '未知';
}

// 顯示警告訊息
function showAlert(message, type = 'info') {
    const alert = document.getElementById('status-alert');
    const messageSpan = document.getElementById('status-message');
    
    alert.className = `alert alert-${type}`;
    messageSpan.textContent = message;
    alert.classList.remove('d-none');
    
    // 3秒後自動隱藏
    setTimeout(() => {
        alert.classList.add('d-none');
    }, 3000);
}

// 確保函數在全局作用域中
window.goToPlayback = function(channelId) {
    console.log('goToPlayback called with channelId:', channelId);
    try {
        const url = `/playback?channel=${channelId}`;
        console.log('Navigating to:', url);
        window.location.href = url;
    } catch (error) {
        console.error('Error in goToPlayback:', error);
        alert('跳轉到歷史回放時發生錯誤: ' + error.message);
    }
};

window.goToChannelPlayback = function() {
    console.log('goToChannelPlayback called, previewChannelId:', previewChannelId);
    try {
        if (previewChannelId !== null) {
            const url = `/playback?channel=${previewChannelId}`;
            console.log('Navigating to:', url);
            window.location.href = url;
        } else {
            console.warn('previewChannelId is null');
            alert('請先選擇一個頻道進行預覽');
        }
    } catch (error) {
        console.error('Error in goToChannelPlayback:', error);
        alert('跳轉到歷史回放時發生錯誤: ' + error.message);
    }
};

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');
    
    try {
        loadChannels();
        console.log('Channels loading initiated');
        
        // 每30秒重新載入頻道狀態
        setInterval(loadChannels, 30000);
        console.log('Auto-refresh interval set');
        
        // 測試函數是否可用
        console.log('Function availability check:', {
            goToPlayback: typeof window.goToPlayback,
            goToChannelPlayback: typeof window.goToChannelPlayback,
            previewChannel: typeof previewChannel,
            loadChannels: typeof loadChannels
        });
        
    } catch (error) {
        console.error('Error during initialization:', error);
    }
});

// Socket.IO事件處理
socket.on('stream_frame', function(data) {
    const img = document.getElementById(`stream-${data.channel_id}`);
    if (img) {
        const blob = new Blob([data.frame], {type: 'image/jpeg'});
        img.src = URL.createObjectURL(blob);
        img.style.display = 'block';
        hideLoading(data.channel_id);
    }

    if (previewChannelId === data.channel_id) {
        const previewImg = document.getElementById('previewImage');
        const blob = new Blob([data.frame], {type: 'image/jpeg'});
        previewImg.src = URL.createObjectURL(blob);
        document.getElementById('preview-loading').style.display = 'none';
        document.getElementById('preview-status').textContent = '已連線';
        document.getElementById('preview-status').className = 'badge bg-success';
    }

    if (previewChannelId === data.channel_id) {
        const fullscreenImg = document.getElementById('fullscreenImage');
        const blob = new Blob([data.frame], {type: 'image/jpeg'});
        fullscreenImg.src = URL.createObjectURL(blob);
    }
});

socket.on('stream_status', function(data) {
    const statusElement = document.getElementById(`status-${data.channel_id}`);
    if (statusElement) {
        statusElement.className = `channel-status status-${data.status}`;
        statusElement.innerHTML = `<i class="fas fa-circle"></i> ${data.message}`;
    }

    if (data.status === 'connecting') {
        showLoading(data.channel_id);
    } else {
        hideLoading(data.channel_id);
    }
});

socket.on('channel_status_update', function(data) {
    // 更新頻道狀態
    data.channels.forEach(channelData => {
        const channel = channels.find(c => c.id === channelData.id);
        if (channel) {
            channel.status = channelData.status;
            channel.recording = channelData.recording;
            updateChannelStatus(channel.id, channel.status);
            updateRecordButton(channel.id, channel.recording);
        }
    });
});
</script>
{% endblock %}