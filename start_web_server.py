#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVRV-Free Web服務器啟動腳本
"""

import os
import sys
import logging
import threading
import webbrowser
import signal
import atexit
from nvrv_web_server import NVRVWebServer

# 引入 pystray 和 PIL
try:
    from pystray import Icon, Menu, MenuItem
    from PIL import Image
except ImportError:
    print("請安裝 pystray 和 Pillow: pip install pystray Pillow")
    sys.exit(1)

# 獲取資料檔案的絕對路徑，處理打包後的環境
def get_data_path(filename):
    if getattr(sys, 'frozen', False):
        base_path = os.path.dirname(sys.executable)
    else:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, filename)

# 全局變數，用於控制Web服務器執行緒
web_server_thread = None
web_server_instance = None
icon = None  # 全局托盤圖標變數

def start_web_server(host, port, debug):
    global web_server_instance
    try:
        web_server_instance = NVRVWebServer()
        
        # 為打包環境優化配置
        if getattr(sys, 'frozen', False):
            # 打包環境：使用最安全的配置
            logging.info("啟動 Web 服務器 (打包模式)")
            web_server_instance.socketio.run(
                web_server_instance.app,
                host=host,
                port=port,
                debug=False,
                use_reloader=False,
                log_output=False  # 關閉 Werkzeug 日誌
            )
        else:
            # 開發環境
            logging.info("啟動 Web 服務器 (開發模式)")
            web_server_instance.run(
                host=host, 
                port=port, 
                debug=debug
            )
            
    except Exception as e:
        # 詳細錯誤記錄
        import traceback
        error_msg = f"Web服務器啟動失敗: {e}"
        error_detail = traceback.format_exc()
        
        logging.error(error_msg)
        logging.error(f"詳細錯誤:\n{error_detail}")
        
        # 在打包環境下寫入錯誤檔案
        if getattr(sys, 'frozen', False):
            try:
                error_file = get_data_path('startup_error.txt')
                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write(f"啟動時間: {datetime.now()}\n")
                    f.write(f"錯誤訊息: {error_msg}\n")
                    f.write(f"詳細錯誤:\n{error_detail}\n")
                    f.write(f"Python 版本: {sys.version}\n")
                    f.write(f"工作目錄: {os.getcwd()}\n")
                    f.write(f"執行檔路徑: {sys.executable}\n")
            except Exception as write_error:
                logging.error(f"無法寫入錯誤檔案: {write_error}")

def open_browser(url):
    webbrowser.open(url)

def exit_application(icon, item):
    global web_server_instance
    if web_server_instance:
        # 停止Flask-SocketIO服務器
        web_server_instance.socketio.stop()
        logging.info("Web服務器已停止。")
    if icon:
        icon.stop()
    sys.exit(0)

def signal_handler(signum, frame):
    """處理 Ctrl+C 信號"""
    if not getattr(sys, 'frozen', False):
        print("\n🛑 收到終止信號，正在關閉程式...")
    global web_server_instance, icon
    if web_server_instance:
        try:
            web_server_instance.socketio.stop()
            logging.info("Web服務器已停止。")
        except Exception as e:
            logging.error(f"停止Web服務器時發生錯誤: {e}")
    if icon:
        try:
            icon.stop()
        except Exception as e:
            logging.error(f"停止托盤圖標時發生錯誤: {e}")
    sys.exit(0)

def cleanup_on_exit():
    """程式退出時的清理工作"""
    global web_server_instance, icon
    if web_server_instance:
        try:
            web_server_instance.socketio.stop()
            logging.info("Web服務器已停止。")
        except Exception as e:
            logging.error(f"停止Web服務器時發生錯誤: {e}")
    if icon:
        try:
            icon.stop()
        except Exception as e:
            logging.error(f"停止托盤圖標時發生錯誤: {e}")

def main():
    global web_server_thread, icon

    # 設置信號處理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup_on_exit)

    # 設置日誌
    log_level = logging.INFO
    if len(sys.argv) > 1 and sys.argv[1] == '--debug':
        log_level = logging.DEBUG

    if getattr(sys, 'frozen', False):
        # 打包後環境：詳細日誌記錄
        log_file = get_data_path('nvrv_web_server.log')
        logging.basicConfig(
            level=logging.DEBUG,  # 打包版本使用 DEBUG 級別
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8', mode='a')
            ]
        )
        logging.info("=== NVRV Web Server 啟動 (EXE模式) ===")
        logging.info(f"Python 版本: {sys.version}")
        logging.info(f"工作目錄: {os.getcwd()}")
        logging.info(f"執行檔路徑: {sys.executable}")
    else:
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    # 檢查配置文件
    settings_path = get_data_path('settings_free.json')
    if not os.path.exists(settings_path):
        error_msg = f"未找到配置文件: {settings_path}"
        logging.error(error_msg)
        if getattr(sys, 'frozen', False):
            # 寫入錯誤檔案
            try:
                error_file = get_data_path('config_error.txt')
                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write(f"錯誤: {error_msg}\n")
                    f.write(f"當前目錄檔案: {os.listdir('.')}\n")
            except:
                pass
        else:
            print("❌ 未找到配置文件 settings_free.json")
        return

    # 載入配置
    try:
        import json
        with open(settings_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"成功載入配置文件，包含 {len(config)} 個頻道")
    except Exception as e:
        logging.error(f"讀取配置文件失敗: {e}")
        return

    # 檢查端口
    port = 8080
    if len(sys.argv) > 2:
        try:
            port = int(sys.argv[2])
        except ValueError:
            logging.warning(f"無效的端口號: {sys.argv[2]}，使用預設端口 8080")

    server_url = f"http://localhost:{port}"
    logging.info(f"準備啟動 Web 服務器: {server_url}")

    # 在單獨的執行緒中啟動Web服務器
    web_server_thread = threading.Thread(
        target=start_web_server, 
        args=('0.0.0.0', port, (log_level == logging.DEBUG)),
        daemon=False  # 改為非守護執行緒
    )
    web_server_thread.start()
    
    # 等待服務器啟動
    import time
    time.sleep(2)
    logging.info("Web 服務器執行緒已啟動")

    # 創建系統托盤圖標
    try:
        image_path = get_data_path('key.ico')
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"圖標檔案不存在: {image_path}")
        
        image = Image.open(image_path)
        logging.info("成功載入托盤圖標")
    except Exception as e:
        logging.error(f"載入圖標失敗: {e}")
        if not getattr(sys, 'frozen', False):
            print(f"❌ 載入圖標失敗: {e}")
        return

    icon = Icon(
        'NVR System',
        image,
        'NVR System',
        Menu(MenuItem('開啟網頁介面', lambda icon, item: open_browser(server_url)),
             MenuItem('結束應用程式', exit_application))
    )

    # 啟動時自動開啟瀏覽器
    if not getattr(sys, 'frozen', False):
        print(f"🌐 Web服務器已啟動: {server_url}")
        print("💡 按 Ctrl+C 可以終止程式")
        print("📌 程式將在系統托盤中運行")

    # 延遲開啟瀏覽器，確保服務器已啟動
    threading.Timer(3.0, lambda: open_browser(server_url)).start()
    logging.info("已設定瀏覽器自動開啟")

    try:
        # 運行系統托盤圖標（這會阻塞主執行緒）
        logging.info("啟動系統托盤")
        icon.run()
    except Exception as e:
        logging.error(f"托盤圖標運行錯誤: {e}")
        if not getattr(sys, 'frozen', False):
            print(f"❌ 托盤圖標運行時發生錯誤: {e}")

if __name__ == '__main__':
    main()



