#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復常見的 EXE 運行問題
"""

import os
import json
import shutil

def fix_missing_files():
    """修復缺失的文件"""
    print("🔧 修復常見問題...")
    
    dist_path = 'dist'
    if not os.path.exists(dist_path):
        print("❌ dist 目錄不存在，請先打包")
        return False
    
    # 1. 檢查並創建 settings_free.json
    settings_path = os.path.join(dist_path, 'settings_free.json')
    if not os.path.exists(settings_path):
        print("🔧 創建預設 settings_free.json...")
        default_settings = [
            {
                "channel_id": 1,
                "channel_name": "演示頻道 1",
                "rtsp_url": "",
                "save_path": "./recordings",
                "is_recording": False
            },
            {
                "channel_id": 2,
                "channel_name": "演示頻道 2", 
                "rtsp_url": "",
                "save_path": "./recordings",
                "is_recording": False
            }
        ]
        
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(default_settings, f, ensure_ascii=False, indent=2)
        print("✅ 已創建預設配置文件")
    
    # 2. 複製 templates 目錄
    templates_src = 'templates'
    templates_dst = os.path.join(dist_path, 'templates')
    if os.path.exists(templates_src) and not os.path.exists(templates_dst):
        print("🔧 複製 templates 目錄...")
        shutil.copytree(templates_src, templates_dst)
        print("✅ 已複製模板文件")
    
    # 3. 複製圖標文件
    icon_src = 'key.ico'
    icon_dst = os.path.join(dist_path, 'key.ico')
    if os.path.exists(icon_src) and not os.path.exists(icon_dst):
        print("🔧 複製圖標文件...")
        shutil.copy2(icon_src, icon_dst)
        print("✅ 已複製圖標文件")
    
    # 4. 創建 recordings 目錄
    recordings_path = os.path.join(dist_path, 'recordings')
    if not os.path.exists(recordings_path):
        print("🔧 創建錄影目錄...")
        os.makedirs(recordings_path)
        print("✅ 已創建錄影目錄")
    
    return True

def create_minimal_test():
    """創建最小化測試版本"""
    print("\n🧪 創建最小化測試版本...")
    
    minimal_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_web_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('settings_free.json', '.'),
        ('templates', 'templates'),
        ('key.ico', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'pystray',
        'PIL.Image',
        'cv2',
        'sqlite3',
        'eventlet',
        'eventlet.wsgi'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='NVRV_WebServer_Minimal',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='key.ico',
)'''
    
    with open('web_server_minimal.spec', 'w', encoding='utf-8') as f:
        f.write(minimal_spec)
    
    print("✅ 已創建 web_server_minimal.spec")

if __name__ == '__main__':
    if fix_missing_files():
        create_minimal_test()
        print("\n" + "=" * 50)
        print("🚀 修復完成！建議步驟:")
        print("1. 測試現有 EXE: debug_exe_simple.bat")
        print("2. 或重新打包最小版本: pyinstaller web_server_minimal.spec")
        print("3. 如果還有問題，請提供錯誤訊息")