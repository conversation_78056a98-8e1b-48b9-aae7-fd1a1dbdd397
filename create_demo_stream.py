#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建演示串流 - 使用靜態圖片模擬RTSP串流
"""

import cv2
import numpy as np
import os
from datetime import datetime
import json

def create_demo_image(channel_name, width=320, height=240):
    """創建演示圖片"""
    # 創建彩色背景
    img = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 根據頻道名稱設定不同顏色
    if "頻道1" in channel_name:
        color = (100, 150, 200)  # 藍色調
    elif "頻道2" in channel_name:
        color = (100, 200, 150)  # 綠色調
    else:
        color = (150, 100, 200)  # 紫色調
    
    img[:] = color
    
    # 添加頻道名稱
    cv2.putText(img, channel_name, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 添加時間戳
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cv2.putText(img, timestamp, (10, height-20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # 添加狀態指示
    cv2.putText(img, "DEMO MODE", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    cv2.putText(img, "320x240 @ 3fps", (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    
    return img

def create_demo_video(channel_name, filename, duration=30):
    """創建演示影片檔案"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(filename, fourcc, 8.0, (320, 240))
    
    frames = duration * 8  # 8fps
    
    for i in range(frames):
        img = create_demo_image(channel_name)
        
        # 添加幀計數
        cv2.putText(img, f"Frame: {i+1}/{frames}", (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
        
        # 添加動態元素
        x = int(50 + 30 * np.sin(i * 0.1))
        cv2.circle(img, (x, 150), 10, (255, 255, 255), -1)
        
        out.write(img)
    
    out.release()
    print(f"✅ 創建演示影片: {filename}")

def main():
    print("🎬 創建演示內容...")
    print("=" * 50)
    
    # 創建靜態目錄
    os.makedirs('./static', exist_ok=True)
    os.makedirs('./recordings', exist_ok=True)
    
    # 讀取設定檔
    if os.path.exists('settings_free.json'):
        with open('settings_free.json', 'r', encoding='utf-8') as f:
            channels = json.load(f)
    else:
        channels = [
            {"channel_name": "測試頻道1", "save_path": "./recordings"},
            {"channel_name": "測試頻道2", "save_path": "./recordings"}
        ]
    
    # 為每個頻道創建演示圖片
    for i, channel in enumerate(channels):
        channel_name = channel.get('channel_name', f'Channel {i+1}')
        
        # 創建靜態圖片
        img = create_demo_image(channel_name)
        img_path = f'./static/demo_channel_{i}.jpg'
        cv2.imwrite(img_path, img)
        print(f"✅ 創建演示圖片: {img_path}")
        
        # 創建演示影片
        today = datetime.now().strftime('%Y%m%d')
        date_dir = os.path.join(channel.get('save_path', './recordings'), today)
        os.makedirs(date_dir, exist_ok=True)
        
        for j in range(2):  # 每個頻道創建2個影片
            timestamp = datetime.now().strftime('%H%M%S')
            video_filename = f"{channel_name}_{today}_{timestamp}_{j:02d}.mp4"
            video_path = os.path.join(date_dir, video_filename)
            create_demo_video(channel_name, video_path, duration=10 + j*5)
    
    print("\n🎉 演示內容創建完成！")
    print("現在Web介面應該能顯示演示畫面")

if __name__ == '__main__':
    main()