#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建演示設定檔
"""

import json
import os

def main():
    print("🎬 創建演示設定檔...")
    
    # 創建演示設定
    demo_settings = [
        {
            "channel_name": "演示頻道1",
            "rtsp_url": "demo://channel1",  # 使用demo://前綴觸發演示模式
            "save_path": "./recordings",
            "record_duration": 20,
            "record_fps": 8,
            "keep_days": 7,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "channel_name": "演示頻道2",
            "rtsp_url": "demo://channel2",
            "save_path": "./recordings",
            "record_duration": 20,
            "record_fps": 8,
            "keep_days": 7,
            "use_source_fps": False,
            "force_640x360": True
        },
        {
            "channel_name": "演示頻道3",
            "rtsp_url": "demo://channel3",
            "save_path": "./recordings",
            "record_duration": 20,
            "record_fps": 8,
            "keep_days": 7,
            "use_source_fps": False,
            "force_640x360": True
        }
    ]
    
    # 備份原設定檔
    if os.path.exists('settings_free.json'):
        os.rename('settings_free.json', 'settings_free.json.backup')
        print("✅ 原設定檔已備份為 settings_free.json.backup")
    
    # 寫入新設定檔
    with open('settings_free.json', 'w', encoding='utf-8') as f:
        json.dump(demo_settings, f, ensure_ascii=False, indent=2)
    
    print("✅ 演示設定檔已創建")
    print("現在Web介面將顯示演示畫面，不需要真實的RTSP串流")

if __name__ == '__main__':
    main()