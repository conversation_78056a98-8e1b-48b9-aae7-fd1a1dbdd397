#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Web介面是否正常運行
"""

import requests
import time
import json

def wait_for_server(max_wait=30):
    """等待服務器啟動"""
    print("⏳ 等待Web服務器啟動...")
    
    for i in range(max_wait):
        try:
            response = requests.get('http://localhost:8080/', timeout=2)
            if response.status_code == 200:
                print("✅ Web服務器已啟動")
                return True
        except:
            pass
        
        print(f"   等待中... ({i+1}/{max_wait})")
        time.sleep(1)
    
    print("❌ Web服務器啟動超時")
    return False

def test_main_page():
    """測試主頁面"""
    print("🔍 測試主頁面...")
    try:
        response = requests.get('http://localhost:8080/', timeout=5)
        if response.status_code == 200:
            print("✅ 主頁面正常")
            return True
        else:
            print(f"❌ 主頁面錯誤: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主頁面錯誤: {e}")
        return False

def test_channels_api():
    """測試頻道API"""
    print("🔍 測試頻道API...")
    try:
        response = requests.get('http://localhost:8080/api/channels', timeout=5)
        data = response.json()
        
        if data.get('success'):
            channels = data.get('channels', [])
            print(f"✅ 頻道API正常，找到 {len(channels)} 個頻道")
            for i, channel in enumerate(channels):
                print(f"   頻道 {i}: {channel.get('name')}")
            return True
        else:
            print(f"❌ 頻道API錯誤: {data.get('error')}")
            return False
    except Exception as e:
        print(f"❌ 頻道API錯誤: {e}")
        return False

def test_playback_page():
    """測試歷史記錄頁面"""
    print("🔍 測試歷史記錄頁面...")
    try:
        response = requests.get('http://localhost:8080/playback', timeout=5)
        if response.status_code == 200:
            print("✅ 歷史記錄頁面正常")
            return True
        else:
            print(f"❌ 歷史記錄頁面錯誤: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 歷史記錄頁面錯誤: {e}")
        return False

def test_recordings_api():
    """測試錄影API"""
    print("🔍 測試錄影API...")
    try:
        response = requests.get('http://localhost:8080/api/recordings?channel_id=0', timeout=5)
        data = response.json()
        
        if data.get('success'):
            recordings = data.get('recordings', [])
            print(f"✅ 錄影API正常，找到 {len(recordings)} 個錄影檔案")
            return True
        else:
            print(f"⚠️  錄影API回應: {data.get('error', '無錄影檔案')}")
            return True  # 沒有錄影檔案是正常的
    except Exception as e:
        print(f"❌ 錄影API錯誤: {e}")
        return False

def main():
    print("🧪 Web介面功能測試")
    print("=" * 50)
    print("請確保Web介面已經啟動")
    print("如果沒有啟動，請執行: python simple_web.py")
    print()
    
    # 等待服務器
    if not wait_for_server():
        print("❌ 無法連接到Web服務器")
        print("請先啟動Web介面: python simple_web.py")
        return
    
    print("\n🔍 開始功能測試...")
    print("=" * 50)
    
    # 測試各個功能
    tests = [
        ("主頁面", test_main_page),
        ("頻道API", test_channels_api),
        ("歷史記錄頁面", test_playback_page),
        ("錄影API", test_recordings_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
        time.sleep(0.5)
    
    print("\n📊 測試結果:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有測試通過！Web介面運行正常")
        print("你可以在瀏覽器中訪問: http://localhost:8080")
    else:
        print("\n⚠️  部分測試失敗，請檢查Web服務器日誌")

if __name__ == '__main__':
    main()