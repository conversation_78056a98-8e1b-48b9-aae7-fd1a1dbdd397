#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下載和準備H.264編碼器庫
用於確保打包時包含所需的編碼器支援
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_file(url, filename):
    """下載文件"""
    try:
        print(f"下載 {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✅ {filename} 下載完成")
        return True
    except Exception as e:
        print(f"❌ 下載 {filename} 失敗: {e}")
        return False

def extract_zip(zip_file, extract_to):
    """解壓縮文件"""
    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"✅ {zip_file} 解壓縮完成")
        return True
    except Exception as e:
        print(f"❌ 解壓縮 {zip_file} 失敗: {e}")
        return False

def setup_codec_libs():
    """設置編碼器庫"""
    print("NVRV-Free 編碼器庫準備工具")
    print("=" * 40)
    
    # 創建庫目錄
    libs_dir = Path("codec_libs")
    libs_dir.mkdir(exist_ok=True)
    
    # OpenH264庫下載 (Cisco提供的免費H.264編碼器)
    openh264_urls = {
        "win64": "https://github.com/cisco/openh264/releases/download/v2.3.1/openh264-2.3.1-win64.dll.bz2",
        "win32": "https://github.com/cisco/openh264/releases/download/v2.3.1/openh264-2.3.1-win32.dll.bz2"
    }
    
    # 檢測系統架構
    import platform
    arch = "win64" if platform.architecture()[0] == "64bit" else "win32"
    
    print(f"檢測到系統架構: {arch}")
    
    # 下載OpenH264
    openh264_url = openh264_urls[arch]
    openh264_file = f"openh264-{arch}.dll.bz2"
    
    if download_file(openh264_url, openh264_file):
        # 解壓縮bz2文件
        try:
            import bz2
            with bz2.BZ2File(openh264_file, 'rb') as f_in:
                with open(libs_dir / "libopenh264.dll", 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            print("✅ OpenH264 DLL 準備完成")
            os.remove(openh264_file)
        except Exception as e:
            print(f"❌ OpenH264 處理失敗: {e}")
    
    # 檢查OpenCV路徑中的編碼器DLL
    try:
        import cv2
        opencv_path = Path(cv2.__file__).parent
        print(f"OpenCV路徑: {opencv_path}")
        
        # 複製OpenCV相關的DLL到庫目錄
        opencv_dlls = [
            "opencv_ffmpeg*.dll",
            "opencv_world*.dll"
        ]
        
        for dll_pattern in opencv_dlls:
            for dll_file in opencv_path.glob(dll_pattern):
                dest_file = libs_dir / dll_file.name
                shutil.copy2(dll_file, dest_file)
                print(f"✅ 複製 {dll_file.name}")
                
    except Exception as e:
        print(f"❌ OpenCV DLL 處理失敗: {e}")
    
    # 創建編碼器測試腳本
    test_script = libs_dir / "test_codecs.py"
    with open(test_script, 'w', encoding='utf-8') as f:
        f.write('''
import cv2
import os
import sys

# 添加當前目錄到DLL搜索路徑
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(os.getcwd())

def test_codec(codec_name):
    try:
        fourcc = cv2.VideoWriter_fourcc(*codec_name)
        writer = cv2.VideoWriter('test.mp4', fourcc, 1, (320, 240))
        result = writer.isOpened()
        writer.release()
        if os.path.exists('test.mp4'):
            os.remove('test.mp4')
        return result
    except:
        return False

codecs = ['H264', 'avc1', 'X264', 'XVID', 'mp4v', 'MJPG']
print("編碼器測試結果:")
for codec in codecs:
    status = "✅" if test_codec(codec) else "❌"
    print(f"{status} {codec}")
''')
    
    print(f"\n✅ 編碼器庫準備完成！")
    print(f"📁 庫文件位置: {libs_dir.absolute()}")
    print(f"🧪 測試腳本: {test_script}")
    print("\n下一步:")
    print("1. 運行 python codec_libs/test_codecs.py 測試編碼器")
    print("2. 運行 build_exe.bat 打包程式")

if __name__ == "__main__":
    setup_codec_libs()