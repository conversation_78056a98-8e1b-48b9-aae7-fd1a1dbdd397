<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按鈕測試</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>歷史回放按鈕測試</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>測試按鈕</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="testGoToPlayback()">
                            測試 goToPlayback(0)
                        </button>
                        <button class="btn btn-secondary me-2" onclick="testGoToChannelPlayback()">
                            測試 goToChannelPlayback()
                        </button>
                        <button class="btn btn-info" onclick="testDirectNavigation()">
                            直接跳轉測試
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>控制台輸出</h5>
                    </div>
                    <div class="card-body">
                        <div id="console-output" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            <!-- 控制台輸出將顯示在這裡 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6>測試說明：</h6>
                    <ul>
                        <li>點擊按鈕測試不同的跳轉函數</li>
                        <li>查看控制台輸出了解函數執行情況</li>
                        <li>檢查瀏覽器開發者工具的控制台是否有錯誤</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let previewChannelId = 0; // 模擬預覽頻道ID
        
        // 重定向原始console.log來顯示在頁面上
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            console.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 測試函數
        function goToPlayback(channelId) {
            console.log('goToPlayback called with channelId:', channelId);
            try {
                const url = `/playback?channel=${channelId}`;
                console.log('Navigating to:', url);
                
                // 在測試頁面中，我們不實際跳轉，只是顯示URL
                addToConsole(`Would navigate to: ${url}`, 'log');
                
                // 取消註釋下面這行來實際跳轉
                // window.location.href = url;
            } catch (error) {
                console.error('Error in goToPlayback:', error);
                alert('跳轉到歷史回放時發生錯誤: ' + error.message);
            }
        }
        
        function goToChannelPlayback() {
            console.log('goToChannelPlayback called, previewChannelId:', previewChannelId);
            try {
                if (previewChannelId !== null) {
                    const url = `/playback?channel=${previewChannelId}`;
                    console.log('Navigating to:', url);
                    
                    // 在測試頁面中，我們不實際跳轉，只是顯示URL
                    addToConsole(`Would navigate to: ${url}`, 'log');
                    
                    // 取消註釋下面這行來實際跳轉
                    // window.location.href = url;
                } else {
                    console.warn('previewChannelId is null');
                    alert('請先選擇一個頻道進行預覽');
                }
            } catch (error) {
                console.error('Error in goToChannelPlayback:', error);
                alert('跳轉到歷史回放時發生錯誤: ' + error.message);
            }
        }
        
        // 測試按鈕函數
        function testGoToPlayback() {
            console.log('Testing goToPlayback function...');
            goToPlayback(0);
        }
        
        function testGoToChannelPlayback() {
            console.log('Testing goToChannelPlayback function...');
            goToChannelPlayback();
        }
        
        function testDirectNavigation() {
            console.log('Testing direct navigation...');
            const url = '/playback?channel=0';
            addToConsole(`Direct navigation to: ${url}`, 'log');
            
            // 取消註釋下面這行來實際跳轉
            // window.location.href = url;
        }
        
        // 頁面載入完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            console.log('Functions available:', {
                goToPlayback: typeof goToPlayback,
                goToChannelPlayback: typeof goToChannelPlayback
            });
        });
    </script>
</body>
</html>