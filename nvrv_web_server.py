#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVRV-Free 現代化Web介面服務器
提供即時監控和歷史回放功能
"""

from flask import Flask, render_template, jsonify, request, send_file, Response, stream_template, redirect, url_for
from flask_socketio import SocketIO, emit
import sys
import cv2
import os
import json
import threading
import time
from datetime import datetime, timedelta
import base64
import glob
import logging
from pathlib import Path
import mimetypes
import numpy as np
from werkzeug.serving import WSGIRequestHandler
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import sqlite3

# --- 路徑處理 ---
# 確保無論在開發環境還是打包後的EXE環境，都能找到正確的資料檔案路徑
def get_data_path(filename):
    """獲取資料檔案的絕對路徑，處理打包後的環境"""
    if getattr(sys, 'frozen', False):
        # 如果是打包後的EXE
        base_path = os.path.dirname(sys.executable)
    else:
        # 如果是開發環境
        base_path = os.path.abspath(".")
    return os.path.join(base_path, filename)

# --- 活動日誌設定 ---
activity_log_path = get_data_path('activity.log')
activity_logger = logging.getLogger('activity')
activity_logger.setLevel(logging.INFO)
# 建立一個FileHandler來寫入日誌檔案
fh = logging.FileHandler(activity_log_path, encoding='utf-8')
fh.setLevel(logging.INFO)
# 建立一個Formatter並設定給Handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
fh.setFormatter(formatter)
# 將Handler加入Logger
if not activity_logger.handlers:
    activity_logger.addHandler(fh)

# 設置主日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Flask-Login 設定 ---
login_manager = LoginManager()
login_manager.login_view = 'login' # 指定登入頁面的路由
login_manager.login_message = "請登入以存取此頁面。"
login_manager.login_message_category = "info"

class User(UserMixin):
    """使用者模型"""
    def __init__(self, id, username, password_hash, role):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.role = role

def get_user_by_username(username):
    conn = sqlite3.connect(get_data_path('users.db'))
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
    user_data = cursor.fetchone()
    conn.close()
    if user_data:
        return User(id=user_data[0], username=user_data[1], password_hash=user_data[2], role=user_data[3])
    return None

@login_manager.user_loader
def load_user(user_id):
    conn = sqlite3.connect(get_data_path('users.db'))
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
    user_data = cursor.fetchone()
    conn.close()
    if user_data:
        return User(id=user_data[0], username=user_data[1], password_hash=user_data[2], role=user_data[3])
    return None

def init_db():
    """初始化資料庫，如果不存在則建立，並加入預設管理員"""
    db_path = get_data_path('users.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    # 建立users資料表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'user'
        )
    ''')
    # 檢查是否已有使用者
    cursor.execute("SELECT COUNT(*) FROM users")
    user_count = cursor.fetchone()[0]
    # 如果沒有使用者，則建立一個預設的admin帳號
    if user_count == 0:
        admin_password = generate_password_hash('admin')
        cursor.execute("INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)",
                       ('admin', admin_password, 'admin'))
        logger.info("資料庫初始化完成，已建立預設管理員帳號 'admin'，密碼 'admin'。")
        activity_logger.info("系統初始化：建立預設管理員帳號 'admin'。")
    
    conn.commit()
    conn.close()


class StreamManager:
    def __init__(self, socketio):
        self.streams = {}
        self.lock = threading.Lock()
        self.socketio = socketio

    def start_stream(self, channel_id, rtsp_url):
        with self.lock:
            if channel_id in self.streams:
                logger.info(f"Stream for channel {channel_id} is already running.")
                return
            
            stream_entry = {'running': True, 'thread': None}
            self.streams[channel_id] = stream_entry
            
            thread = threading.Thread(target=self._run_stream, args=(channel_id, rtsp_url, stream_entry), daemon=True)
            stream_entry['thread'] = thread
            thread.start()
            logger.info(f"Started stream thread for channel {channel_id}.")

    def stop_stream(self, channel_id):
        with self.lock:
            if channel_id in self.streams:
                logger.info(f"Stopping stream for channel {channel_id}.")
                self.streams[channel_id]['running'] = False
            else:
                logger.info(f"Stream for channel {channel_id} was not running.")

    def _run_stream(self, channel_id, rtsp_url, stream_entry):
        cap = None
        logger.info(f"Stream thread started for channel {channel_id}.")
        
        while stream_entry.get('running', False):
            try:
                if cap is None or not cap.isOpened():
                    logger.info(f"[{channel_id}] Connecting to RTSP stream: {rtsp_url}")
                    self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'connecting', 'message': '正在連接...'})
                    
                    cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    try:
                        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
                        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
                    except Exception as e:
                        logger.warning(f"[{channel_id}] Could not set timeout properties: {e}")

                    if not cap.isOpened():
                        logger.warning(f"[{channel_id}] Failed to open RTSP stream.")
                        self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'error', 'message': '連線失敗'})
                        if cap: cap.release()
                        cap = None
                        time.sleep(5)
                        continue
                    
                    logger.info(f"[{channel_id}] RTSP stream connected successfully.")
                    self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'online', 'message': '已連線'})

                ret, frame = cap.read()
                if ret:
                    frame = cv2.resize(frame, (480, 270), interpolation=cv2.INTER_LINEAR)
                    ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 60])
                    if ret:
                        self.socketio.emit('stream_frame', {'channel_id': channel_id, 'frame': buffer.tobytes()})
                    time.sleep(1/15)
                else:
                    logger.warning(f"[{channel_id}] Lost connection to stream. Reconnecting...")
                    self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'reconnecting', 'message': '訊號遺失，嘗試重連...'})
                    if cap: cap.release()
                    cap = None
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Error in stream thread for channel {channel_id}: {e}")
                self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'error', 'message': '串流錯誤'})
                if cap: cap.release()
                cap = None
                time.sleep(5)

        if cap:
            cap.release()
        with self.lock:
            if channel_id in self.streams:
                del self.streams[channel_id]
        logger.info(f"Stream thread for channel {channel_id} has stopped.")
        self.socketio.emit('stream_status', {'channel_id': channel_id, 'status': 'offline', 'message': '已中斷連線'})

stream_manager = None

from functools import wraps

# ... (之前的程式碼)

def admin_required(f):
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'admin':
            return jsonify({'success': False, 'error': '權限不足'}), 403
        return f(*args, **kwargs)
    return decorated_function

class NVRVWebServer:
    def __init__(self, nvrv_instance=None):
        global stream_manager
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'nvrv-free-web-2024'
        self.app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)

        # 初始化 LoginManager
        login_manager.init_app(self.app)

        # 初始化資料庫
        init_db()

        # 修復打包後的async_mode問題 - 改進版本
        try:
            # 在打包環境中，優先使用 threading 模式
            if getattr(sys, 'frozen', False):
                # 打包環境：強制使用 threading 模式
                self.socketio = SocketIO(
                    self.app, 
                    cors_allowed_origins="*", 
                    async_mode='threading',
                    logger=False,  # 關閉 SocketIO 日誌
                    engineio_logger=False  # 關閉 EngineIO 日誌
                )
                logger.info("SocketIO 初始化成功 (threading 模式)")
            else:
                # 開發環境：讓 SocketIO 自動選擇
                self.socketio = SocketIO(self.app, cors_allowed_origins="*")
                logger.info("SocketIO 初始化成功 (自動模式)")
                
        except Exception as e:
            logger.error(f"SocketIO 初始化失敗: {e}")
            # 如果 threading 失敗，嘗試不指定 async_mode
            try:
                self.socketio = SocketIO(
                    self.app, 
                    cors_allowed_origins="*",
                    logger=False,
                    engineio_logger=False
                )
                logger.info("SocketIO 初始化成功 (fallback 模式)")
            except Exception as e2:
                logger.error(f"SocketIO fallback 初始化也失敗: {e2}")
                raise e2
        if stream_manager is None:
            stream_manager = StreamManager(self.socketio)
        self.nvrv_instance = nvrv_instance
        self.channels_config = []
        self.stream_threads = {}
        self.active_streams = {}
        
        # 載入頻道配置
        self.load_channels_config()
        
        # 設置路由
        self.setup_routes()
        self.setup_socketio()
        
    def load_channels_config(self):
        """載入頻道配置"""
        try:
            if os.path.exists('settings_free.json'):
                with open('settings_free.json', 'r', encoding='utf-8') as f:
                    self.channels_config = json.load(f)
                
                # 統計頻道信息
                rtsp_count = sum(1 for ch in self.channels_config if ch.get('rtsp_url', ''))
                empty_count = len(self.channels_config) - rtsp_count
                
                logger.info(f"Loaded {len(self.channels_config)} channels from settings_free.json")
                logger.info(f"  - Channels with RTSP: {rtsp_count}")
                logger.info(f"  - Empty channels: {empty_count}")
                
                # 顯示前幾個頻道的詳細信息
                for i, ch in enumerate(self.channels_config[:3]):
                    name = ch.get('channel_name', f'Channel {i+1}')
                    rtsp_url = ch.get('rtsp_url', '')
                    save_path = ch.get('save_path', './recordings')
                    
                    logger.info(f"  Channel {i+1}: {name}")
                    if rtsp_url:
                        logger.info(f"    RTSP: {rtsp_url[:50]}...")
                    logger.info(f"    Save path: {save_path}")
                
                if len(self.channels_config) > 3:
                    logger.info(f"  ... and {len(self.channels_config) - 3} more channels")
                    
            else:
                logger.warning("settings_free.json not found")
                self.channels_config = []
        except Exception as e:
            logger.error(f"Error loading channels config: {e}")
            self.channels_config = []
    
    def setup_routes(self):
        """設置Web路由"""
        
        @self.app.route('/')
        @login_required
        def index():
            """主頁面 - 即時監控"""
            return render_template('index.html')

        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            if request.method == 'POST':
                username = request.form.get('username')
                password = request.form.get('password')
                user = get_user_by_username(username)
                
                if user and check_password_hash(user.password_hash, password):
                    login_user(user, remember=False)
                    activity_logger.info(f"使用者 '{username}' 從 IP {request.remote_addr} 登入成功。")
                    return jsonify({'success': True})
                else:
                    activity_logger.warning(f"使用者 '{username}' 從 IP {request.remote_addr} 登入失敗。")
                    return jsonify({'success': False, 'error': '無效的使用者名稱或密碼'})
            
            return render_template('login.html')

        @self.app.route('/logout')
        @login_required
        def logout():
            activity_logger.info(f"使用者 '{current_user.username}' 已登出。")
            logout_user()
            return redirect(url_for('login'))

        @self.app.route('/user_management')
        @admin_required
        def user_management():
            activity_logger.info(f"管理員 '{current_user.username}' 存取了使用者管理頁面。")
            return render_template('user_management.html')

        @self.app.route('/activity_log')
        @admin_required
        def activity_log_page():
            activity_logger.info(f"管理員 '{current_user.username}' 存取了活動日誌頁面。")
            return render_template('activity_log.html')

        @self.app.route('/api/activity_log')
        @admin_required
        def get_activity_log():
            try:
                date_filter = request.args.get('date')
                user_filter = request.args.get('username')

                with open(get_data_path('activity.log'), 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                filtered_lines = lines

                if date_filter:
                    filtered_lines = [line for line in filtered_lines if line.startswith(date_filter)]
                
                if user_filter:
                    # 篩選包含特定使用者名稱的日誌
                    user_pattern = f"'{user_filter}'"
                    filtered_lines = [line for line in filtered_lines if user_pattern in line]

                filtered_lines.reverse() #最新的在最上面
                log_data = filtered_lines[:200] # 最多返回200筆

                return jsonify({'success': True, 'log': log_data})
            except FileNotFoundError:
                return jsonify({'success': True, 'log': ['尚未產生任何活動日誌。']})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/users', methods=['GET'])
        @admin_required
        def get_users():
            conn = sqlite3.connect(get_data_path('users.db'))
            cursor = conn.cursor()
            cursor.execute("SELECT id, username, role FROM users")
            users = [{'id': r[0], 'username': r[1], 'role': r[2]} for r in cursor.fetchall()]
            conn.close()
            return jsonify({'success': True, 'users': users})

        @self.app.route('/api/users', methods=['POST'])
        @admin_required
        def add_user():
            data = request.json
            username = data.get('username')
            password = data.get('password')
            role = data.get('role', 'user')

            if not username or not password:
                return jsonify({'success': False, 'error': '使用者名稱和密碼為必填項'})

            conn = sqlite3.connect(get_data_path('users.db'))
            cursor = conn.cursor()
            try:
                hashed_password = generate_password_hash(password)
                cursor.execute("INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)", 
                               (username, hashed_password, role))
                conn.commit()
                activity_logger.info(f"管理員 '{current_user.username}' 新增了使用者 '{username}'。")
                return jsonify({'success': True})
            except sqlite3.IntegrityError:
                return jsonify({'success': False, 'error': '使用者名稱已存在'})
            finally:
                conn.close()

        @self.app.route('/api/users/<int:user_id>', methods=['PUT'])
        @admin_required
        def update_user(user_id):
            data = request.json
            password = data.get('password')

            if not password:
                return jsonify({'success': False, 'error': '密碼為必填項'})

            hashed_password = generate_password_hash(password)
            conn = sqlite3.connect(get_data_path('users.db'))
            cursor = conn.cursor()
            cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", (hashed_password, user_id))
            conn.commit()
            conn.close()
            activity_logger.info(f"管理員 '{current_user.username}' 更新了使用者ID {user_id} 的密碼。")
            return jsonify({'success': True})

        @self.app.route('/api/users/<int:user_id>', methods=['DELETE'])
        @admin_required
        def delete_user(user_id):
            if user_id == current_user.id:
                return jsonify({'success': False, 'error': '無法刪除自己'})

            conn = sqlite3.connect(get_data_path('users.db'))
            cursor = conn.cursor()
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
            conn.commit()
            conn.close()
            activity_logger.info(f"管理員 '{current_user.username}' 刪除了使用者ID {user_id}。")
            return jsonify({'success': True})
        
        @self.app.route('/playback')
        @login_required
        def playback():
            activity_logger.info(f"使用者 '{current_user.username}' 存取了歷史回放頁面。")
            return render_template('playback.html')
        def playback():
            """回放頁面"""
            return render_template('playback.html')
        

        
        @self.app.route('/api/channels')
        def get_channels():
            """獲取所有頻道信息"""
            try:
                channels = []
                for i, channel_data in enumerate(self.channels_config):
                    rtsp_url = channel_data.get('rtsp_url', '')
                    
                    if not rtsp_url:
                        continue

                    # 快速判斷狀態，不進行實際連線測試
                    if rtsp_url:
                        if rtsp_url.startswith('demo://'):
                            status = 'demo'
                        else:
                            status = 'ready'  # 表示有RTSP URL但未測試連線
                    else:
                        status = 'offline'
                    
                    channels.append({
                        'id': i,
                        'name': channel_data.get('channel_name', f'Channel {i+1}'),
                        'rtsp_url': rtsp_url,
                        'recording': False,  # TODO: 從NVRV實例獲取實際狀態
                        'status': status,
                        'save_path': channel_data.get('save_path', './recordings')
                    })
                
                return jsonify({'success': True, 'channels': channels})
            except Exception as e:
                logger.error(f"Error getting channels: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/recordings')
        def get_recordings():
            """獲取錄影檔案列表"""
            try:
                channel_id = request.args.get('channel_id')
                date = request.args.get('date', datetime.now().strftime('%Y%m%d'))
                
                if not channel_id or int(channel_id) >= len(self.channels_config):
                    return jsonify({'success': True, 'recordings': []})
                
                channel_data = self.channels_config[int(channel_id)]
                save_path = channel_data.get('save_path', './recordings')
                channel_name = channel_data.get('channel_name', f'Channel {int(channel_id)+1}')
                
                recordings = self.scan_recordings(save_path, channel_name, date)
                
                return jsonify({'success': True, 'recordings': recordings})
            except Exception as e:
                logger.error(f"Error getting recordings: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/video/<path:filename>')
        def serve_video(filename):
            """提供影片檔案"""
            try:
                logger.info(f"Serving video file: {filename}")
                
                file_path = self.find_video_file(filename)
                logger.info(f"Found video file path: {file_path}")
                
                if file_path and os.path.exists(file_path):
                    # 使用 Flask 的 send_file 直接提供檔案
                    return send_file(
                        file_path,
                        mimetype='video/mp4',
                        as_attachment=False,
                        download_name=filename
                    )
                else:
                    logger.error(f"Video file not found: {filename}")
                    return jsonify({'error': f'File not found: {filename}'}), 404
            except Exception as e:
                logger.error(f"Error serving video {filename}: {e}")
                return jsonify({'error': str(e)}), 500
        
        
        
        @self.app.route('/api/test_stream/<int:channel_id>')
        def test_stream(channel_id):
            """測試串流連線"""
            try:
                if channel_id >= len(self.channels_config):
                    return jsonify({'success': False, 'error': 'Channel not found'})
                
                rtsp_url = self.channels_config[channel_id].get('rtsp_url', '')
                if not rtsp_url:
                    return jsonify({'success': False, 'error': 'No RTSP URL configured'})
                
                if rtsp_url.startswith('demo://'):
                    return jsonify({'success': True, 'message': 'Demo stream ready'})
                
                # 快速測試RTSP連線（設定短超時）
                import threading
                import time
                
                result = {'success': False, 'error': 'Connection timeout'}
                
                def test_connection():
                    try:
                        cap = cv2.VideoCapture(rtsp_url)
                        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                        # 移除不支援的超時設定
                        
                        if cap.isOpened():
                            ret, frame = cap.read()
                            cap.release()
                            if ret and frame is not None:
                                result['success'] = True
                                result['message'] = 'Stream connection OK'
                            else:
                                result['error'] = 'Cannot read frames from stream'
                        else:
                            result['error'] = 'Cannot open RTSP stream'
                    except Exception as e:
                        result['error'] = f'Connection error: {str(e)}'
                
                # 在背景線程中測試連線，最多等待5秒
                test_thread = threading.Thread(target=test_connection)
                test_thread.daemon = True
                test_thread.start()
                test_thread.join(timeout=5)
                
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
    
    def setup_socketio(self):
        """設置WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info('Client connected')
            emit('status', {'message': 'Connected to NVRV-Free'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info('Client disconnected')
        
        @self.socketio.on('request_channel_status')
        def handle_channel_status():
            """發送頻道狀態更新"""
            channels = []
            for i, channel_data in enumerate(self.channels_config):
                rtsp_url = channel_data.get('rtsp_url', '')
                status = 'offline'
                if rtsp_url:
                    status = self.check_rtsp_status(rtsp_url)
                
                channels.append({
                    'id': i,
                    'status': status,
                    'recording': False  # TODO: 從NVRV實例獲取
                })
            
            emit('channel_status_update', {'channels': channels})

        @self.socketio.on('start_stream')
        def handle_start_stream(data):
            channel_id = data['channel_id']
            if channel_id < len(self.channels_config):
                channel_name = self.channels_config[channel_id].get('channel_name', f'Channel {channel_id+1}')
                activity_logger.info(f"使用者 '{current_user.username}' 開始觀看頻道 '{channel_name}' (ID: {channel_id})。")
                rtsp_url = self.channels_config[channel_id].get('rtsp_url', '')
                if rtsp_url:
                    stream_manager.start_stream(channel_id, rtsp_url)

        @self.socketio.on('stop_stream')
        def handle_stop_stream(data):
            channel_id = data['channel_id']
            stream_manager.stop_stream(channel_id)
    
    def check_rtsp_status(self, rtsp_url):
        """檢查RTSP連線狀態"""
        if not rtsp_url:
            return 'offline'
        
        if rtsp_url.startswith('demo://'):
            return 'demo'
        
        # 對於真實的RTSP URL，暫時返回offline狀態
        # 避免在載入時進行耗時的連接測試
        return 'offline'
    
    
    
    def scan_recordings(self, save_path, channel_name, date):
        """掃描錄影檔案"""
        recordings = []
        
        try:
            logger.info(f"Scanning recordings in {save_path} for channel {channel_name}, date {date}")
            
            if not os.path.exists(save_path):
                logger.warning(f"Save path does not exist: {save_path}")
                return recordings
            
            all_files = []
            
            # 搜尋指定日期目錄
            date_dir = os.path.join(save_path, date)
            logger.debug(f"Checking date directory: {date_dir}")
            
            if os.path.exists(date_dir):
                # 搜尋日期目錄中的所有 mp4 檔案
                for file_path in glob.glob(os.path.join(date_dir, "*.mp4")):
                    all_files.append(file_path)
                logger.info(f"Found {len(all_files)} files in date directory")
            
            # 如果沒找到，搜尋根目錄和所有子目錄
            if not all_files:
                logger.info("No files in date directory, searching all subdirectories...")
                try:
                    for root, dirs, files in os.walk(save_path):
                        for file in files:
                            if file.lower().endswith('.mp4'):
                                file_path = os.path.join(root, file)
                                all_files.append(file_path)
                    logger.info(f"Found {len(all_files)} files in all directories")
                except Exception as e:
                    logger.error(f"Error walking directory {save_path}: {e}")
            
            # 去重並按修改時間排序（最新的在前）
            all_files = list(set(all_files))
            all_files.sort(key=lambda x: os.path.getmtime(x) if os.path.exists(x) else 0, reverse=True)
            
            logger.info(f"Processing {len(all_files)} video files...")
            
            for file_path in all_files:
                try:
                    if not os.path.exists(file_path):
                        continue
                        
                    file_stat = os.stat(file_path)
                    filename = os.path.basename(file_path)

                    # 排除以temp開頭的檔案
                    if filename.startswith('temp'):
                        continue
                    
                    # 檢查檔案大小，跳過太小的檔案（可能是損壞的）
                    if file_stat.st_size < 1024:  # 小於 1KB
                        logger.debug(f"Skipping small file: {filename} ({file_stat.st_size} bytes)")
                        continue
                    
                    # duration = self.get_video_duration(file_path)
                    duration = 0 # Temporarily disable duration calculation for speed
                    
                    recordings.append({
                        'filename': filename,
                        'path': file_path,
                        'size': file_stat.st_size,
                        'created': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                        'duration': duration
                    })
                    
                    logger.debug(f"Added recording: {filename} ({file_stat.st_size} bytes, {duration:.1f}s)")
                    
                except Exception as e:
                    logger.error(f"Error processing file {file_path}: {e}")
                    continue
            
            logger.info(f"Successfully scanned {len(recordings)} recordings")
        
        except Exception as e:
            logger.error(f"Error scanning recordings: {e}")
        
        return recordings
    
    def get_video_duration(self, file_path):
        """獲取影片時長"""
        try:
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                return duration
            return 0
        except:
            return 0
    
    def find_video_file(self, filename):
        """查找影片檔案的完整路徑"""
        try:
            logger.info(f"Searching for video file: {filename}")
            
            # 首先嘗試從檔案名稱中提取頻道資訊
            search_paths = []
            
            # 根據檔案名稱模式判斷可能的頻道
            for i, channel_data in enumerate(self.channels_config):
                save_path = channel_data.get('save_path', './recordings')
                channel_name = channel_data.get('channel_name', f'Channel {i+1}')
                
                # 如果檔案名稱包含頻道名稱，優先搜尋該頻道
                if channel_name in filename:
                    search_paths.insert(0, (save_path, channel_name, i))
                else:
                    search_paths.append((save_path, channel_name, i))
            
            # 搜尋所有可能的路徑
            for save_path, channel_name, channel_id in search_paths:
                logger.debug(f"Searching in channel {channel_id} ({channel_name}): {save_path}")
                
                if not os.path.exists(save_path):
                    logger.debug(f"Save path does not exist: {save_path}")
                    continue
                
                # 搜尋根目錄
                file_path = os.path.join(save_path, filename)
                logger.debug(f"Checking root path: {file_path}")
                if os.path.exists(file_path):
                    logger.info(f"Found video file in root: {file_path}")
                    return file_path
                
                # 搜尋所有子目錄（包括日期目錄）
                try:
                    for root, dirs, files in os.walk(save_path):
                        if filename in files:
                            file_path = os.path.join(root, filename)
                            logger.info(f"Found video file in subdirectory: {file_path}")
                            return file_path
                except Exception as e:
                    logger.error(f"Error walking directory {save_path}: {e}")
                    continue
            
            # 如果還是找不到，嘗試在所有錄影目錄中進行全域搜尋
            logger.warning(f"File not found in channel directories, performing global search...")
            
            all_search_paths = set()
            for channel_data in self.channels_config:
                save_path = channel_data.get('save_path', './recordings')
                if os.path.exists(save_path):
                    all_search_paths.add(save_path)
            
            for search_path in all_search_paths:
                try:
                    for root, dirs, files in os.walk(search_path):
                        if filename in files:
                            file_path = os.path.join(root, filename)
                            logger.info(f"Found video file in global search: {file_path}")
                            return file_path
                except Exception as e:
                    logger.error(f"Error in global search of {search_path}: {e}")
                    continue
            
            logger.error(f"Video file not found anywhere: {filename}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding video file {filename}: {e}")
            return None
    
    def run(self, host='0.0.0.0', port=8080, debug=False):
        """啟動Web服務器"""
        logger.info(f"Starting NVRV-Free Web Server on http://{host}:{port}")
        
        # 使用自定義的請求處理器來減少日誌輸出
        class QuietWSGIRequestHandler(WSGIRequestHandler):
            def log_request(self, code='-', size='-'):
                # 只記錄錯誤請求
                if str(code).startswith('4') or str(code).startswith('5'):
                    super().log_request(code, size)
        
        self.socketio.run(
            self.app, 
            host=host, 
            port=port, 
            debug=debug,
            request_handler=QuietWSGIRequestHandler if not debug else None
        )

# 獨立運行Web服務器
if __name__ == '__main__':
    web_server = NVRVWebServer()
    web_server.run(debug=True)
