#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建簡單的應用程式圖標
"""

from PIL import Image, ImageDraw
import os

def create_simple_icon():
    """創建一個簡單的 ICO 圖標文件"""
    # 創建 32x32 的圖像
    size = 32
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 繪製一個簡單的攝影機圖標
    # 主體矩形
    draw.rectangle([4, 8, 24, 20], fill=(70, 130, 180), outline=(50, 100, 150))
    
    # 鏡頭
    draw.ellipse([6, 10, 14, 18], fill=(200, 200, 200), outline=(150, 150, 150))
    draw.ellipse([8, 12, 12, 16], fill=(50, 50, 50))
    
    # 觀景窗
    draw.rectangle([20, 6, 28, 10], fill=(100, 100, 100))
    
    # 腳架
    draw.line([16, 20, 16, 28], fill=(100, 100, 100), width=2)
    draw.line([12, 28, 20, 28], fill=(100, 100, 100), width=2)
    
    # 保存為 ICO 文件
    img.save('key.ico', format='ICO', sizes=[(32, 32)])
    print("✅ 已創建 key.ico 圖標文件")

if __name__ == '__main__':
    if not os.path.exists('key.ico'):
        create_simple_icon()
    else:
        print("✅ key.ico 已存在")