#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
機器授權驗證模組
可以集成到任何Python項目中進行機器授權驗證
"""

import hashlib
import platform
import os
import sys
import tkinter as tk
from tkinter import messagebox


class MachineAuthorization:
    """機器授權驗證類"""
    
    def __init__(self, license_file="machine.id"):
        """
        初始化授權驗證
        
        Args:
            license_file (str): 授權檔案名稱，預設為 machine.id
        """
        self.license_file = license_file
        self.signature_key = "AUTHORIZED_2024"  # 可以修改為您的專用密鑰
    
    def get_machine_id(self):
        """
        獲取機器唯一ID
        
        Returns:
            str: 32位MD5機器ID
        """
        try:
            # 組合多個硬體信息生成唯一ID
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            return hashlib.md5(machine_info.encode()).hexdigest()
        except:
            # 如果獲取硬體信息失敗，使用備用方案
            import uuid
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()
    
    def generate_signature(self, machine_id):
        """
        生成授權簽名
        
        Args:
            machine_id (str): 機器ID
            
        Returns:
            str: 授權簽名
        """
        return hashlib.md5(f"{self.signature_key}_{machine_id}_2024".encode()).hexdigest()
    
    def show_authorization_error(self, current_machine_id, authorized_id="", error_type=""):
        """
        顯示授權錯誤對話框，包含複製機器ID功能
        
        Args:
            current_machine_id (str): 當前機器ID
            authorized_id (str): 授權的機器ID
            error_type (str): 錯誤類型
        """
        # 創建自定義對話框
        dialog = tk.Toplevel()
        dialog.title("授權錯誤")
        dialog.geometry("500x350")
        dialog.configure(bg="#ECEFF1")
        dialog.resizable(False, False)
        dialog.grab_set()  # 模態對話框
        
        # 居中顯示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (350 // 2)
        dialog.geometry(f"500x350+{x}+{y}")
        
        # 錯誤圖標和標題
        title_frame = tk.Frame(dialog, bg="#ECEFF1")
        title_frame.pack(pady=20)
        
        tk.Label(title_frame, text="⚠️", font=("Arial", 32), bg="#ECEFF1", fg="#FF5722").pack()
        tk.Label(title_frame, text="軟體授權錯誤", font=("Roboto", 16, "bold"), 
                bg="#ECEFF1", fg="#D32F2F").pack(pady=5)
        
        # 錯誤信息
        info_frame = tk.Frame(dialog, bg="#FFFFFF", relief=tk.RAISED, bd=2)
        info_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        if error_type == "機器ID不匹配":
            error_msg = f"此軟體未授權在此機器上運行！\n\n當前機器ID與授權ID不匹配。"
        elif error_type == "授權檔案簽名無效":
            error_msg = f"授權檔案簽名無效！\n\n檔案可能被篡改或損壞。"
        elif error_type == "未找到授權檔案":
            error_msg = f"未找到授權檔案！\n\n此軟體需要有效的授權檔案才能運行。"
        else:
            error_msg = f"授權驗證失敗！\n\n{error_type}"
        
        tk.Label(info_frame, text=error_msg, font=("Roboto", 12), 
                bg="#FFFFFF", fg="#333333", justify=tk.LEFT).pack(pady=15)
        
        # 機器ID信息框
        id_frame = tk.Frame(info_frame, bg="#F5F5F5", relief=tk.SUNKEN, bd=1)
        id_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(id_frame, text="當前機器ID:", font=("Roboto", 10, "bold"), 
                bg="#F5F5F5", fg="#333333").pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        # 機器ID顯示和複製
        id_display_frame = tk.Frame(id_frame, bg="#F5F5F5")
        id_display_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        id_entry = tk.Entry(id_display_frame, font=("Consolas", 10), bg="#FFFFFF", 
                           fg="#333333", relief=tk.FLAT, bd=1, state="readonly")
        id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        id_entry.config(state="normal")
        id_entry.insert(0, current_machine_id)
        id_entry.config(state="readonly")
        
        def copy_machine_id():
            dialog.clipboard_clear()
            dialog.clipboard_append(current_machine_id)
            copy_btn.config(text="已複製!", bg="#4CAF50")
            dialog.after(2000, lambda: copy_btn.config(text="複製", bg="#2196F3"))
        
        copy_btn = tk.Button(id_display_frame, text="複製", font=("Roboto", 9), 
                           bg="#2196F3", fg="white", width=8, relief=tk.FLAT,
                           command=copy_machine_id)
        copy_btn.pack(side=tk.RIGHT)
        
        if authorized_id:
            tk.Label(id_frame, text=f"授權機器ID: {authorized_id}", font=("Roboto", 10), 
                    bg="#F5F5F5", fg="#666666").pack(anchor=tk.W, padx=10, pady=(0, 10))
        
        # 說明文字
        instruction_text = ("請將上方的機器ID提供給管理員，\n"
                          "管理員需要使用授權工具生成對應的授權檔案。")
        tk.Label(info_frame, text=instruction_text, font=("Roboto", 10), 
                bg="#FFFFFF", fg="#666666", justify=tk.CENTER).pack(pady=10)
        
        # 按鈕
        button_frame = tk.Frame(dialog, bg="#ECEFF1")
        button_frame.pack(pady=15)
        
        tk.Button(button_frame, text="確定", font=("Roboto", 12, "bold"), 
                 bg="#F44336", fg="white", width=12, relief=tk.FLAT,
                 command=dialog.destroy).pack()
        
        # 等待對話框關閉
        dialog.wait_window()
    
    def check_authorization(self, show_gui=True, exit_on_fail=True):
        """
        檢查機器授權
        
        Args:
            show_gui (bool): 是否顯示GUI錯誤對話框，False則只打印錯誤
            exit_on_fail (bool): 授權失敗時是否退出程式
            
        Returns:
            bool: 授權是否通過
        """
        current_machine_id = self.get_machine_id()
        
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, 'r') as f:
                    content = f.read().strip()
                
                # 檢查是否為有效的授權格式（應該包含簽名）
                if '|' not in content:
                    error_msg = "授權檔案格式錯誤"
                    if show_gui:
                        self.show_authorization_error(current_machine_id, "", error_msg)
                    else:
                        print(f"授權錯誤: {error_msg}")
                    
                    if exit_on_fail:
                        sys.exit(1)
                    return False
                
                authorized_id, signature = content.split('|', 1)
                
                # 驗證機器ID
                if current_machine_id != authorized_id:
                    if show_gui:
                        self.show_authorization_error(current_machine_id, authorized_id, "機器ID不匹配")
                    else:
                        print(f"授權錯誤: 機器ID不匹配 - 當前: {current_machine_id}, 授權: {authorized_id}")
                    
                    if exit_on_fail:
                        sys.exit(1)
                    return False
                
                # 驗證簽名
                expected_signature = self.generate_signature(authorized_id)
                if signature != expected_signature:
                    if show_gui:
                        self.show_authorization_error(current_machine_id, authorized_id, "授權檔案簽名無效")
                    else:
                        print(f"授權錯誤: 簽名無效")
                    
                    if exit_on_fail:
                        sys.exit(1)
                    return False
                    
                print(f"機器授權驗證成功，機器ID: {current_machine_id}")
                return True
                
            except Exception as e:
                error_msg = f"讀取授權檔案錯誤: {e}"
                if show_gui:
                    self.show_authorization_error(current_machine_id, "", error_msg)
                else:
                    print(f"授權錯誤: {error_msg}")
                
                if exit_on_fail:
                    sys.exit(1)
                return False
        else:
            # 沒有授權檔案
            if show_gui:
                self.show_authorization_error(current_machine_id, "", "未找到授權檔案")
            else:
                print(f"授權錯誤: 未找到授權檔案 - 機器ID: {current_machine_id}")
            
            if exit_on_fail:
                sys.exit(1)
            return False


# 便利函數
def check_machine_license(license_file="machine.id", show_gui=True, exit_on_fail=True):
    """
    便利函數：檢查機器授權
    
    Args:
        license_file (str): 授權檔案路徑
        show_gui (bool): 是否顯示GUI錯誤對話框
        exit_on_fail (bool): 授權失敗時是否退出程式
        
    Returns:
        bool: 授權是否通過
    """
    auth = MachineAuthorization(license_file)
    return auth.check_authorization(show_gui, exit_on_fail)


def get_current_machine_id():
    """
    便利函數：獲取當前機器ID
    
    Returns:
        str: 當前機器ID
    """
    auth = MachineAuthorization()
    return auth.get_machine_id()


# 測試代碼
if __name__ == "__main__":
    print("機器授權驗證模組測試")
    print(f"當前機器ID: {get_current_machine_id()}")
    
    # 測試授權檢查
    try:
        if check_machine_license(exit_on_fail=False):
            print("授權驗證通過！")
        else:
            print("授權驗證失敗！")
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
