#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Web介面頻道顯示
"""

import requests
import json
import threading
import time
from nvrv_web_server import NVRVWebServer

def start_server():
    """啟動測試服務器"""
    web_server = NVRVWebServer()
    web_server.run(host='127.0.0.1', port=8083, debug=False)

def test_channel_display():
    """測試頻道顯示"""
    print("🚀 啟動測試服務器...")
    
    # 在背景啟動服務器
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服務器啟動
    time.sleep(3)
    
    try:
        # 測試頻道API
        print("🔍 測試頻道載入...")
        response = requests.get('http://127.0.0.1:8083/api/channels', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                channels = data.get('channels', [])
                print(f"✅ 成功載入 {len(channels)} 個頻道")
                
                # 顯示所有頻道的詳細信息
                print(f"\n📋 頻道列表:")
                rtsp_count = 0
                empty_count = 0
                
                for i, channel in enumerate(channels):
                    name = channel.get('name', f'Channel {i+1}')
                    rtsp_url = channel.get('rtsp_url', '')
                    save_path = channel.get('save_path', './recordings')
                    status = channel.get('status', 'unknown')
                    
                    if rtsp_url:
                        rtsp_count += 1
                        status_icon = "🟢" if status == 'online' else "🔴" if status == 'offline' else "⚠️"
                        print(f"   {i+1:2d}. {status_icon} {name}")
                        print(f"       RTSP: {rtsp_url[:60]}...")
                        print(f"       儲存: {save_path}")
                    else:
                        empty_count += 1
                        print(f"   {i+1:2d}. ⚪ {name} (未設定RTSP)")
                        print(f"       儲存: {save_path}")
                    print()
                
                print(f"📊 統計:")
                print(f"   總頻道數: {len(channels)}")
                print(f"   有RTSP的頻道: {rtsp_count}")
                print(f"   空白頻道: {empty_count}")
                
                # 檢查儲存路徑
                save_paths = set(ch.get('save_path', './recordings') for ch in channels)
                print(f"   使用的儲存路徑: {len(save_paths)} 個")
                
                print(f"\n✅ Web介面已正確載入settings_free.json中的所有頻道配置")
                print(f"🌐 您可以在瀏覽器中訪問: http://localhost:8080")
                print(f"   - 即時監控: http://localhost:8080/")
                print(f"   - 歷史回放: http://localhost:8080/playback")
                
            else:
                print(f"❌ API錯誤: {data.get('error')}")
        else:
            print(f"❌ HTTP錯誤: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 連接失敗: {e}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == '__main__':
    test_channel_display()