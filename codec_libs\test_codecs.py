
import cv2
import os
import sys

# 添加當前目錄到DLL搜索路徑
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(os.getcwd())

def test_codec(codec_name):
    try:
        fourcc = cv2.VideoWriter_fourcc(*codec_name)
        writer = cv2.VideoWriter('test.mp4', fourcc, 1, (320, 240))
        result = writer.isOpened()
        writer.release()
        if os.path.exists('test.mp4'):
            os.remove('test.mp4')
        return result
    except:
        return False

codecs = ['H264', 'avc1', 'X264', 'XVID', 'mp4v', 'MJPG']
print("編碼器測試結果:")
for codec in codecs:
    status = "✅" if test_codec(codec) else "❌"
    print(f"{status} {codec}")
