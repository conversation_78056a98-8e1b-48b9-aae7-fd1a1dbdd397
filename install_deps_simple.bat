@echo off
echo Installing NVRV Web Server dependencies...

pip install --upgrade pip
pip install --upgrade flask==2.3.3
pip install --upgrade flask-socketio==5.3.6
pip install --upgrade python-socketio==5.8.0
pip install --upgrade python-engineio==4.7.1
pip install --upgrade eventlet==0.33.3
pip install --upgrade pystray==0.19.4
pip install --upgrade pillow==10.0.1
pip install --upgrade opencv-python==********
pip install --upgrade numpy==1.24.3
pip install --upgrade flask-login==0.6.3
pip install --upgrade werkzeug==2.3.7
pip install --upgrade pyinstaller==6.1.0

echo Dependencies installation completed
pause