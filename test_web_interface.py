#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVRV-Free Web介面測試腳本
"""

import requests
import json
import time
import sys
from datetime import datetime

class WebInterfaceTest:
    def __init__(self, base_url='http://localhost:8080'):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_server_connection(self):
        """測試服務器連接"""
        print("🔍 測試服務器連接...")
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ 服務器連接正常")
                return True
            else:
                print(f"❌ 服務器回應錯誤: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 無法連接到服務器: {e}")
            return False
    
    def test_api_channels(self):
        """測試頻道API"""
        print("🔍 測試頻道API...")
        try:
            response = self.session.get(f"{self.base_url}/api/channels", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    channels = data.get('channels', [])
                    print(f"✅ 頻道API正常，找到 {len(channels)} 個頻道")
                    
                    # 顯示頻道詳情
                    for i, channel in enumerate(channels[:5]):  # 只顯示前5個
                        status = channel.get('status', 'unknown')
                        name = channel.get('name', 'Unknown')
                        rtsp_url = channel.get('rtsp_url', '')
                        
                        status_icon = {
                            'online': '🟢',
                            'offline': '🔴',
                            'demo': '🟡',
                            'error': '⚠️'
                        }.get(status, '❓')
                        
                        print(f"   {status_icon} 頻道 {i}: {name} ({status})")
                        if rtsp_url:
                            url_display = rtsp_url[:50] + "..." if len(rtsp_url) > 50 else rtsp_url
                            print(f"      URL: {url_display}")
                    
                    if len(channels) > 5:
                        print(f"   ... 還有 {len(channels) - 5} 個頻道")
                    
                    return True, channels
                else:
                    print(f"❌ 頻道API錯誤: {data.get('error', 'Unknown error')}")
                    return False, []
            else:
                print(f"❌ 頻道API回應錯誤: {response.status_code}")
                return False, []
        except requests.exceptions.RequestException as e:
            print(f"❌ 頻道API請求失敗: {e}")
            return False, []
    
    def test_stream_endpoints(self, channels):
        """測試串流端點"""
        print("🔍 測試串流端點...")
        
        if not channels:
            print("⚠️  沒有頻道可測試")
            return
        
        # 測試前3個頻道的串流
        test_channels = channels[:3]
        
        for channel in test_channels:
            channel_id = channel.get('id')
            channel_name = channel.get('name', f'Channel {channel_id}')
            
            print(f"   測試頻道 {channel_id}: {channel_name}")
            
            try:
                # 測試串流端點（只獲取前幾個字節）
                stream_url = f"{self.base_url}/api/live_stream/{channel_id}"
                response = self.session.get(stream_url, timeout=5, stream=True)
                
                if response.status_code == 200:
                    # 讀取一些數據來確認串流正常
                    chunk = next(response.iter_content(chunk_size=1024), None)
                    if chunk and b'Content-Type: image/jpeg' in chunk:
                        print(f"   ✅ 頻道 {channel_id} 串流正常")
                    else:
                        print(f"   ⚠️  頻道 {channel_id} 串流格式異常")
                else:
                    print(f"   ❌ 頻道 {channel_id} 串流錯誤: {response.status_code}")
                
                response.close()
                
            except requests.exceptions.RequestException as e:
                print(f"   ❌ 頻道 {channel_id} 串流測試失敗: {e}")
            
            # 避免同時測試太多串流
            time.sleep(1)
    
    def test_recordings_api(self, channels):
        """測試錄影API"""
        print("🔍 測試錄影API...")
        
        if not channels:
            print("⚠️  沒有頻道可測試")
            return
        
        # 測試第一個頻道的錄影
        channel = channels[0]
        channel_id = channel.get('id')
        channel_name = channel.get('name', f'Channel {channel_id}')
        
        print(f"   測試頻道 {channel_id}: {channel_name}")
        
        try:
            today = datetime.now().strftime('%Y%m%d')
            recordings_url = f"{self.base_url}/api/recordings?channel_id={channel_id}&date={today}"
            response = self.session.get(recordings_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    recordings = data.get('recordings', [])
                    print(f"   ✅ 錄影API正常，找到 {len(recordings)} 個錄影檔案")
                    
                    # 顯示前幾個錄影檔案
                    for i, recording in enumerate(recordings[:3]):
                        filename = recording.get('filename', 'Unknown')
                        size = recording.get('size', 0)
                        duration = recording.get('duration', 0)
                        
                        size_mb = size / (1024 * 1024) if size > 0 else 0
                        duration_str = f"{int(duration//60)}:{int(duration%60):02d}" if duration > 0 else "0:00"
                        
                        print(f"      📹 {filename} ({size_mb:.1f}MB, {duration_str})")
                    
                    if len(recordings) > 3:
                        print(f"      ... 還有 {len(recordings) - 3} 個錄影檔案")
                else:
                    print(f"   ❌ 錄影API錯誤: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ 錄影API回應錯誤: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 錄影API測試失敗: {e}")
    
    def test_pages(self):
        """測試頁面載入"""
        print("🔍 測試頁面載入...")
        
        pages = [
            ('/', '主頁面'),
            ('/playback', '歷史回放'),
            ('/settings', '系統設定')
        ]
        
        for path, name in pages:
            try:
                response = self.session.get(f"{self.base_url}{path}", timeout=5)
                if response.status_code == 200:
                    if '<html' in response.text.lower():
                        print(f"   ✅ {name} 載入正常")
                    else:
                        print(f"   ⚠️  {name} 內容異常")
                else:
                    print(f"   ❌ {name} 載入錯誤: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ❌ {name} 載入失敗: {e}")
    
    def run_all_tests(self):
        """執行所有測試"""
        print("=" * 50)
        print("NVRV-Free Web介面功能測試")
        print("=" * 50)
        print(f"測試目標: {self.base_url}")
        print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 測試服務器連接
        if not self.test_server_connection():
            print("\n❌ 服務器連接失敗，無法繼續測試")
            return False
        
        print()
        
        # 2. 測試頁面載入
        self.test_pages()
        print()
        
        # 3. 測試頻道API
        success, channels = self.test_api_channels()
        print()
        
        if success and channels:
            # 4. 測試串流端點
            self.test_stream_endpoints(channels)
            print()
            
            # 5. 測試錄影API
            self.test_recordings_api(channels)
            print()
        
        print("=" * 50)
        print("測試完成")
        print("=" * 50)
        
        return True

def main():
    """主函數"""
    # 解析命令行參數
    base_url = 'http://localhost:8080'
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        if not base_url.startswith('http'):
            base_url = f'http://{base_url}'
    
    # 執行測試
    tester = WebInterfaceTest(base_url)
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n👋 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == '__main__':
    main()