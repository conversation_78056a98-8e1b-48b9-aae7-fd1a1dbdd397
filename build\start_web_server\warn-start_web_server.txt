
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed), psutil (optional), backports.tarfile (optional), gevent.subprocess (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional), setuptools._distutils.tests.test_util (delayed)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), backports.tarfile (optional), gevent.subprocess (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), IPython.utils.timing (optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), eventlet.patcher (delayed, conditional, optional)
runtime module named distutils.sysconfig - imported by cffi._shimmed_dist_utils (optional), setuptools._distutils.util (delayed, conditional), setuptools._distutils.extension (delayed), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.cygwinccompiler (delayed), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_cygwinccompiler (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_mingwccompiler (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_unixccompiler (top-level), setuptools._distutils.tests.test_util (top-level)
runtime module named distutils - imported by distutils._log (top-level), distutils._macos_compat (top-level), distutils._modified (top-level), distutils._msvccompiler (top-level), distutils.archive_util (top-level), distutils.ccompiler (top-level), distutils.cmd (top-level), distutils.command (top-level), distutils.compat (top-level), distutils.core (top-level), distutils.cygwinccompiler (top-level), distutils.debug (top-level), distutils.dep_util (top-level), distutils.dir_util (top-level), distutils.dist (top-level), distutils.errors (top-level), distutils.extension (top-level), distutils.fancy_getopt (top-level), distutils.file_util (top-level), distutils.filelist (top-level), distutils.log (top-level), distutils.spawn (top-level), distutils.sysconfig (top-level), distutils.tests (top-level), distutils.text_file (top-level), distutils.unixccompiler (top-level), distutils.util (top-level), distutils.version (top-level), distutils.versionpredicate (top-level), distutils.zosccompiler (top-level), setuptools.discovery (top-level), setuptools.errors (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools.command.bdist_wheel (top-level), cffi._shimmed_dist_utils (optional), setuptools._distutils.util (delayed, conditional), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.sdist (top-level), setuptools._distutils.cygwinccompiler (delayed), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_ccompiler (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_cygwinccompiler (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_mingwccompiler (top-level), setuptools._distutils.tests.test_msvccompiler (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_unixccompiler (top-level), setuptools._distutils.tests.test_util (top-level), setuptools._distutils.tests.test_version (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), xmlrpc.server (optional), eventlet.greenio.base (delayed, optional), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional)
runtime module named distutils.util - imported by setuptools._core_metadata (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (top-level), setuptools._distutils.extension (delayed), setuptools._distutils.unixccompiler (delayed, conditional), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_mingwccompiler (top-level), setuptools._distutils.tests.test_msvccompiler (top-level), setuptools._distutils.tests.test_unixccompiler (top-level), setuptools._distutils.tests.test_util (top-level)
missing module named pytest - imported by trio.testing._raises_group (conditional, optional), setuptools._vendor.typeguard._pytest_plugin (conditional), setuptools._distutils.tests.support (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.unix_compat (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_ccompiler (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_cygwinccompiler (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_extension (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_data (top-level), setuptools._distutils.tests.test_install_headers (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_mingwccompiler (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_msvccompiler (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_unixccompiler (top-level), setuptools._distutils.tests.test_util (top-level), setuptools._distutils.tests.test_version (top-level)
runtime module named distutils.version - imported by gevent.testing.sysinfo (delayed), setuptools._distutils.tests.test_version (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named termios - imported by getpass (optional), tty (top-level), werkzeug._reloader (delayed, optional), click._termui_impl (conditional), psutil._compat (delayed, optional), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), pygments.formatters.img (optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), gevent.tests.test__issue600 (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), gevent.testing.testrunner (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
runtime module named distutils.unixccompiler - imported by setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_unixccompiler (top-level)
runtime module named distutils.compat - imported by distutils.compat.py38 (top-level), distutils.compat.py39 (top-level), setuptools._distutils.tests.test_unixccompiler (top-level)
missing module named path - imported by setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named 'jaraco.path' - imported by setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_text_file (top-level)
runtime module named distutils.text_file - imported by setuptools._distutils.extension (delayed), setuptools._distutils.sysconfig (delayed), setuptools._distutils.tests.test_text_file (top-level)
runtime module named distutils.tests - imported by distutils.tests.compat (top-level), distutils.tests.support (top-level), distutils.tests.test_archive_util (top-level), distutils.tests.test_bdist (top-level), distutils.tests.test_bdist_dumb (top-level), distutils.tests.test_bdist_rpm (top-level), distutils.tests.test_build (top-level), distutils.tests.test_build_clib (top-level), distutils.tests.test_build_ext (top-level), distutils.tests.test_build_py (top-level), distutils.tests.test_build_scripts (top-level), distutils.tests.test_ccompiler (top-level), distutils.tests.test_check (top-level), distutils.tests.test_clean (top-level), distutils.tests.test_cmd (top-level), distutils.tests.test_config_cmd (top-level), distutils.tests.test_core (top-level), distutils.tests.test_cygwinccompiler (top-level), distutils.tests.test_dir_util (top-level), distutils.tests.test_dist (top-level), distutils.tests.test_extension (top-level), distutils.tests.test_file_util (top-level), distutils.tests.test_filelist (top-level), distutils.tests.test_install (top-level), distutils.tests.test_install_data (top-level), distutils.tests.test_install_headers (top-level), distutils.tests.test_install_lib (top-level), distutils.tests.test_install_scripts (top-level), distutils.tests.test_log (top-level), distutils.tests.test_mingwccompiler (top-level), distutils.tests.test_modified (top-level), distutils.tests.test_msvccompiler (top-level), distutils.tests.test_sdist (top-level), distutils.tests.test_spawn (top-level), distutils.tests.test_sysconfig (top-level), distutils.tests.test_text_file (top-level), distutils.tests.test_unixccompiler (top-level), distutils.tests.test_util (top-level), distutils.tests.test_version (top-level), distutils.tests.test_versionpredicate (top-level), distutils.tests.unix_compat (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_bdist (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_clean (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_cygwinccompiler (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_data (top-level), setuptools._distutils.tests.test_install_headers (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_install_scripts (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_msvccompiler (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named test.support - imported by test (optional), gevent.testing.monkey_test (optional), gevent.testing.support (delayed, conditional, optional), setuptools._vendor.importlib_resources.tests.test_functional (optional), setuptools._distutils.tests.compat.py38 (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level)
missing module named test.test_support - imported by test (delayed, conditional, optional), gevent.testing.support (delayed, conditional, optional)
missing module named 'jaraco.envs' - imported by setuptools._distutils.tests.test_sysconfig (top-level)
runtime module named distutils.ccompiler - imported by cffi._shimmed_dist_utils (optional), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_ccompiler (top-level), setuptools._distutils.tests.test_msvccompiler (delayed), setuptools._distutils.tests.test_sysconfig (top-level)
runtime module named distutils.spawn - imported by setuptools._distutils.cmd (delayed), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_spawn (top-level)
runtime module named distutils.filelist - imported by setuptools.monkey (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils.command.sdist - imported by setuptools.command.sdist (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils.command - imported by distutils.command._framework_compat (top-level), distutils.command.bdist (top-level), distutils.command.bdist_dumb (top-level), distutils.command.bdist_rpm (top-level), distutils.command.build (top-level), distutils.command.build_clib (top-level), distutils.command.build_ext (top-level), distutils.command.build_py (top-level), distutils.command.build_scripts (top-level), distutils.command.check (top-level), distutils.command.clean (top-level), distutils.command.config (top-level), distutils.command.install (top-level), distutils.command.install_data (top-level), distutils.command.install_egg_info (top-level), distutils.command.install_headers (top-level), distutils.command.install_lib (top-level), distutils.command.install_scripts (top-level), distutils.command.sdist (top-level), setuptools.dist (top-level), setuptools._distutils.dist (delayed), setuptools._distutils.tests.test_build_ext (delayed), setuptools._distutils.tests.test_install (top-level)
runtime module named distutils.archive_util - imported by setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils._msvccompiler - imported by setuptools._distutils.tests.test_msvccompiler (top-level)
runtime module named distutils._modified - imported by setuptools._distutils.file_util (delayed), setuptools._distutils.tests.test_modified (top-level)
runtime module named distutils.cygwinccompiler - imported by setuptools._distutils.tests.test_cygwinccompiler (top-level), setuptools._distutils.tests.test_mingwccompiler (delayed)
runtime module named distutils._log - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_log (top-level)
runtime module named distutils.command.install_scripts - imported by setuptools._distutils.tests.test_install_scripts (top-level)
runtime module named distutils.extension - imported by setuptools.extension (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_extension (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_lib (top-level)
runtime module named distutils.command.install_lib - imported by setuptools._distutils.tests.test_install_lib (top-level)
runtime module named distutils.command.install_headers - imported by setuptools._distutils.tests.test_install_headers (top-level)
runtime module named distutils.command.install_data - imported by setuptools._distutils.tests.test_install_data (top-level)
runtime module named distutils.command.install - imported by setuptools._distutils.tests.test_install (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline3.keysyms.common (optional), pytz.tzinfo (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional), IPython.utils._process_cli (top-level)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level), IPython.utils._process_cli (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
runtime module named distutils.debug - imported by setuptools.dist (top-level), setuptools._distutils.ccompiler (delayed), setuptools._distutils.cmd (delayed), setuptools._distutils.filelist (delayed), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_filelist (top-level)
runtime module named distutils.file_util - imported by setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_file_util (top-level)
runtime module named distutils.tests.test_dist - imported by setuptools._distutils.tests.test_dist (delayed)
runtime module named distutils.dist - imported by setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (top-level), wheel.cli.convert (optional), setuptools._vendor.wheel.cli.convert (optional), setuptools._distutils.cmd (delayed), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_dist (top-level)
runtime module named distutils.cmd - imported by setuptools.dist (top-level), setuptools._distutils.dist (delayed), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_dist (top-level)
runtime module named distutils.dir_util - imported by setuptools.command.bdist_egg (top-level), cffi._shimmed_dist_utils (optional), setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_dir_util (top-level)
runtime module named distutils.command.config - imported by setuptools._distutils.tests.test_config_cmd (top-level)
runtime module named distutils.command.clean - imported by setuptools._distutils.tests.test_clean (top-level)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed)
missing module named chardet - imported by pygments.lexer (delayed, conditional, optional), requests (optional)
runtime module named distutils.command.check - imported by setuptools._distutils.tests.test_check (top-level)
runtime module named distutils.command.build_scripts - imported by setuptools._distutils.tests.test_build_scripts (top-level)
runtime module named distutils.command.build_py - imported by setuptools._distutils.tests.test_build_py (top-level)
missing module named xx - imported by setuptools._distutils.tests.test_build_ext (delayed)
runtime module named distutils.tests.support - imported by setuptools._distutils.tests.test_build_ext (top-level)
runtime module named distutils.command.build_clib - imported by setuptools._distutils.tests.test_build_clib (top-level)
runtime module named distutils.command.build - imported by setuptools.command.build (top-level), setuptools._distutils.tests.test_build (top-level)
runtime module named distutils.command.bdist_rpm - imported by setuptools._distutils.tests.test_bdist_rpm (top-level)
runtime module named distutils.command.bdist_dumb - imported by setuptools._distutils.tests.test_bdist_dumb (top-level)
runtime module named distutils.command.bdist - imported by setuptools.command (top-level), setuptools._distutils.tests.test_bdist (top-level)
missing module named 'test.support.import_helper' - imported by setuptools._distutils.tests.compat.py38 (optional)
missing module named 'test.support.os_helper' - imported by setuptools._distutils.tests.compat.py38 (optional)
missing module named 'test.support.warnings_helper' - imported by setuptools._vendor.importlib_resources.tests.test_functional (optional), setuptools._distutils.tests.compat.py38 (optional)
runtime module named distutils.versionpredicate - imported by setuptools._distutils.dist (delayed)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by werkzeug._internal (conditional), pkg_resources (conditional), setuptools.command.bdist_wheel (conditional), trio._file_io (conditional), trio._path (conditional), prompt_toolkit.eventloop.inputhook (conditional), asgiref.sync (conditional), jaraco.collections (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'docutils.utils' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.parsers' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.nodes' - imported by setuptools._distutils.command.check (top-level)
missing module named docutils - imported by setuptools._distutils.command.check (top-level)
runtime module named distutils.fancy_getopt - imported by setuptools.dist (top-level), setuptools._distutils.ccompiler (delayed), setuptools._distutils.cmd (delayed)
missing module named typeshed - imported by typeguard._decorators (conditional)
missing module named 'typeshed.stdlib' - imported by setuptools._vendor.typeguard._decorators (conditional)
missing module named mod - imported by setuptools._vendor.importlib_resources.tests.test_files (delayed)
missing module named 'jaraco.test' - imported by setuptools._vendor.importlib_resources.tests.compat.py39 (top-level)
runtime module named distutils.command.build_ext - imported by cffi._shimmed_dist_utils (optional), setuptools (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_install (top-level)
runtime module named distutils.errors - imported by setuptools.errors (top-level), setuptools.config.expand (top-level), setuptools.extension (top-level), setuptools.dist (top-level), setuptools.archive_util (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.installer (top-level), setuptools (top-level), cffi._shimmed_dist_utils (optional), setuptools.msvc (top-level), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_mingwccompiler (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_msvccompiler (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_unixccompiler (top-level), setuptools._distutils.tests.test_util (top-level)
runtime module named distutils.core - imported by setuptools.extension (top-level), setuptools.dist (top-level), setuptools (top-level), cffi._shimmed_dist_utils (optional), setuptools._distutils.dist (delayed), setuptools._distutils.tests.support (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_scripts (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils.log - imported by setuptools.logging (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools.command.bdist_wheel (top-level), cffi._shimmed_dist_utils (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named _watchdog_fsevents - imported by watchdog.observers.fsevents (top-level)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional), flask.ctx (conditional), flask.testing (conditional), flask.cli (conditional), flask.app (conditional)
missing module named 'yapf.yapflib' - imported by IPython.terminal.interactiveshell (delayed)
missing module named yapf - imported by IPython.terminal.interactiveshell (delayed)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), matplotlib.backends.qt_compat (delayed, conditional), PyQt5 (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), asttokens.asttokens (top-level), pystray._base (top-level), pystray._win32 (top-level), pystray._xorg (top-level)
missing module named six.moves.xrange - imported by six.moves (top-level), asttokens.asttokens (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), pystray._appindicator (top-level), pystray._util.gtk (top-level), pystray._util.notify_dbus (top-level), pystray._gtk (top-level)
missing module named PyQt6 - imported by matplotlib.backends.qt_compat (delayed, conditional), matplotlib.backends.backend_qtagg (delayed, conditional, optional)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named PySide2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named shiboken6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named PySide6 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named backend_rust - imported by zstandard (conditional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional), eventlet.green.OpenSSL.crypto (top-level)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level), eventlet.green.OpenSSL.SSL (top-level)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named jsonpointer - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named cPickle - imported by pickleshare (optional), pycparser.ply.yacc (delayed, optional)
missing module named cStringIO - imported by cPickle (top-level), cffi.ffiplatform (optional)
missing module named copy_reg - imported by cPickle (top-level), cStringIO (top-level)
missing module named pathlib2 - imported by pickleshare (optional)
missing module named 'curio.meta' - imported by sniffio._impl (delayed, conditional)
missing module named exceptiongroup - imported by trio._core._run (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional)
missing module named _pytest - imported by trio.testing._raises_group (conditional)
missing module named 'distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), eventlet.patcher (optional), eventlet.tpool (optional), gevent._compat (optional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__monkey (delayed, optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional), gevent.tests.test__threading_2 (optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named hypothesis - imported by trio._core._run (delayed)
missing module named tputil - imported by trio._core._concat_tb (optional)
missing module named OpenSSL - imported by trio._dtls (delayed, conditional), eventlet.greenio.base (optional), eventlet.green.OpenSSL.SSL (top-level)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named 'astroid.node_classes' - imported by asttokens.astroid_compat (optional)
missing module named 'astroid.nodes' - imported by asttokens.astroid_compat (optional)
missing module named astroid - imported by asttokens.astroid_compat (optional)
missing module named 'ipykernel.kernelapp' - imported by IPython (delayed)
missing module named ipykernel - imported by IPython (delayed)
missing module named pexpect - imported by IPython.utils._process_posix (top-level)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named monotonic - imported by eventlet.hubs.hub (optional), eventlet (optional)
missing module named itimer - imported by eventlet.hubs.hub (conditional, optional)
missing module named psycopg2 - imported by eventlet.support.psycopg2_patcher (top-level)
missing module named 'OpenSSL.version' - imported by eventlet.green.OpenSSL.version (top-level)
missing module named 'OpenSSL.tsafe' - imported by eventlet.green.OpenSSL.tsafe (top-level)
missing module named socketio.socketio_manage - imported by socketio (optional), flask_socketio (optional)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named aio_pika - imported by socketio.async_aiopika_manager (optional)
missing module named 'aioredis.exceptions' - imported by socketio.async_redis_manager (optional)
missing module named aioredis - imported by socketio.async_redis_manager (optional)
missing module named 'redis.exceptions' - imported by socketio.async_redis_manager (optional)
missing module named redis - imported by socketio.redis_manager (optional), socketio.async_redis_manager (optional)
missing module named kafka - imported by socketio.kafka_manager (optional)
missing module named 'backports.socketpair' - imported by gevent._socketcommon (optional)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named Queue - imported by gevent.queue (conditional)
missing module named httplib - imported by gevent.tests.test__socket_ssl (optional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named _import_wait - imported by gevent.tests.test__import_wait (top-level)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
missing module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named urllib2 - imported by gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (top-level)
missing module named 'gevent.resolver.cares' - imported by gevent.ares (top-level), gevent.tests.test__ares_host_result (optional)
missing module named __builtin__ - imported by gevent._ffi.loop (conditional), gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named 'test.libregrtest' - imported by gevent.testing.resources (delayed, optional)
missing module named 'test.lock_tests' - imported by gevent.testing.monkey_test (optional)
missing module named objgraph - imported by gevent.testing.leakcheck (optional)
missing module named mock - imported by gevent.testing (optional)
missing module named cares - imported by gevent.resolver.ares (top-level)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named kombu - imported by socketio.kombu_manager (optional)
missing module named msgpack - imported by socketio.msgpack_packet (top-level)
missing module named ipdb - imported by geventwebsocket.utf8validator (delayed, conditional, optional)
missing module named 'wsaccel.utf8validator' - imported by geventwebsocket.utf8validator (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named 'gi.repository' - imported by pystray._appindicator (top-level), pystray._util.gtk (top-level), pystray._util.notify_dbus (top-level), pystray._gtk (top-level)
missing module named 'Xlib.XK' - imported by pystray._xorg (top-level)
missing module named 'Xlib.threaded' - imported by pystray._xorg (top-level)
missing module named Xlib - imported by pystray._xorg (top-level)
missing module named PyObjCTools - imported by pystray._darwin (top-level)
missing module named objc - imported by pystray._darwin (top-level)
missing module named Foundation - imported by pystray._darwin (top-level)
missing module named AppKit - imported by pystray._darwin (top-level)
