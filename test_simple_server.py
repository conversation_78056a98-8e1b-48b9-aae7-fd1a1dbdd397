#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版本的服務器測試，用於調試打包問題
"""

import sys
import os
from flask import Flask
from flask_socketio import Socket<PERSON>

def test_socketio_modes():
    """測試不同的 SocketIO 模式"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test'
    
    modes_to_test = ['threading', 'eventlet', None]
    
    for mode in modes_to_test:
        try:
            print(f"測試 async_mode: {mode}")
            if mode:
                socketio = SocketIO(app, async_mode=mode, cors_allowed_origins="*")
            else:
                socketio = SocketIO(app, cors_allowed_origins="*")
            print(f"✅ {mode} 模式成功")
            return mode, socketio
        except Exception as e:
            print(f"❌ {mode} 模式失敗: {e}")
    
    return None, None

if __name__ == '__main__':
    print("🧪 測試 SocketIO 模式相容性")
    print(f"Python 版本: {sys.version}")
    print(f"打包環境: {getattr(sys, 'frozen', False)}")
    
    mode, socketio = test_socketio_modes()
    
    if socketio:
        print(f"✅ 找到可用模式: {mode}")
        
        @socketio.on('connect')
        def handle_connect():
            print('客戶端已連接')
        
        try:
            print("🚀 啟動測試服務器...")
            socketio.run(app, host='127.0.0.1', port=8081, debug=False)
        except Exception as e:
            print(f"❌ 服務器啟動失敗: {e}")
    else:
        print("❌ 沒有找到可用的 SocketIO 模式")